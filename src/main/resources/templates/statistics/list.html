<!DOCTYPE html>
<html lang="es" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Estadísticas</title>
    <th:block th:replace="~{layout/header :: header}"></th:block>
</head>
<body hx-boost="true">
<!-- Define the title variable -->
<th:block
        th:with="pageTitle=${spot != null ? 'Plaza '+ spot.getSpotNumber() + ' - Sótano '+ spot.getFloor().getFloorNumber() : 'Estadísticas'}">
    <!-- Pass the variable to the navbar -->
    <th:block th:replace="~{layout/navbar :: navbar(${pageTitle})}"></th:block>
</th:block>
<th:block th:replace="~{layout/navbar :: floating-action('')}"></th:block>
<div class="container-sm mb-5">
    <!-- Summary Statistics Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-header py-2 py-md-2">
            <div class="row align-items-center">
                <div class="col-md-6 mb-2 mb-md-0 py-1">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart-fill me-2"></i>Estadísticas
                    </h5>
                </div>

                <div class="col-md-6 d-flex justify-content-md-end py-1">
                    <div class="col-12 col-md-8 col-lg-6 px-0">
                        <div class="input-group w-100">
                            <span class="input-group-text"><i class="bi bi-car-front-fill"></i></span>
                            <select class="form-select" hx-include="#spotId" hx-push-url="true"
                                    hx-swap="innerHTML"
                                    hx-target="body"
                                    hx-trigger="change"
                                    id="spotId"
                                    name="spotId"
                                    th:attr="hx-get=@{/statistics(selectedDay=${selectedDay != null ? selectedDay : ''}, selectedMonth=${selectedMonth != null ? selectedMonth : ''}, selectedYear=${selectedYear != null ? selectedYear : ''}, activeTab=${activeTab != null ? activeTab : 'hourly'})}">
                                <option value="">Seleccione una plaza</option>
                                <option th:each="spot : ${spots}" th:selected="${selectedSpotId != null && selectedSpotId == spot.id}"
                                        th:text="${'Plaza ' + spot.spotNumber + ' - Sótano ' + spot.floor.floorNumber}"
                                        th:value="${spot.id}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- No spot selected message -->
            <div class="alert alert-info" th:if="${selectedSpotId == null}">
                Por favor, seleccione una plaza para ver las estadísticas de consumo.
            </div>
            <!-- No results message - only show when a spot is selected but no data -->
            <div class="alert alert-info"
                 th:if="${selectedSpotId != null && !hasAnyReadings}">
                No hay datos de consumo disponibles para esta plaza. Por favor, asegúrese de que existen lecturas en la base de datos.
            </div>

            <!-- No results for filters message - only show when a spot has readings but none match the filters -->
            <div class="alert alert-info"
                 th:if="${selectedSpotId != null && hasAnyReadings && (timeSeriesData == null || timeSeriesData.isEmpty())}">
                No hay datos que coincidan con los filtros seleccionados.
            </div>

            <!-- Statistics summary - only show when a spot is selected and has data -->
            <div class="row" th:if="${selectedSpotId != null && timeSeriesData != null && !timeSeriesData.isEmpty()}">
                <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h6 class="card-subtitle mb-2 text-muted">Hoy</h6>
                                <p class="card-text display-6"
                                   th:text="${#numbers.formatDecimal(todayConsumption, 1, 2) + ' kWh'}">0.00 kWh</p>
                                <p class="card-text text-muted small"
                                   th:text="'Consumo del ' + ${#temporals.format(#temporals.createNow(), 'dd/MM/yyyy')}"></p>
                            </div>
                        </div>
                </div>
                <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h6 class="card-subtitle mb-2 text-muted">Este Mes</h6>
                                <p class="card-text display-6"
                                   th:text="${#numbers.formatDecimal(thisMonthConsumption, 1, 2) + ' kWh'}">0.00 kWh</p>
                                <p class="card-text text-muted small"
                                   th:text="'Consumo de ' + ${currentYearMonth.getMonth().getDisplayName(T(java.time.format.TextStyle).FULL, T(java.util.Locale).forLanguageTag('es'))} + ' de ' + ${currentYearMonth.getYear()}"
                                   th:with="currentYearMonth=${T(java.time.YearMonth).now()}"></p>
                            </div>
                        </div>
                </div>
                <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <h6 class="card-subtitle mb-2 text-muted">Este Año</h6>
                                <p class="card-text display-6"
                                   th:text="${#numbers.formatDecimal(thisYearConsumption, 1, 2) + ' kWh'}">0.00 kWh</p>
                                <p class="card-text text-muted small"
                                   th:text="'Consumo del año ' + ${#temporals.year(#temporals.createNow())}"></p>
                            </div>
                        </div>
                </div>
            </div>

            <!-- Advanced Analytics Section -->
            <div class="row" th:if="${selectedSpotId != null && utilizationMetrics != null}">
                <!-- Utilization Metrics -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted">
                                <i class="bi bi-speedometer2 me-1"></i>Utilización
                            </h6>
                            <p class="card-text display-6"
                               th:text="${#numbers.formatDecimal(utilizationMetrics.getUtilizationPercentage(), 1, 1) + '%'}"
                               th:classappend="${utilizationMetrics.hasGoodUtilization() ? 'text-success' : 'text-warning'}">0.0%</p>
                            <p class="card-text text-muted small">
                                <span th:text="${utilizationMetrics.activeSessions}">0</span> sesiones activas
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Efficiency Metrics -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted">
                                <i class="bi bi-lightning-charge me-1"></i>Eficiencia
                            </h6>
                            <p class="card-text display-6"
                               th:text="${#numbers.formatDecimal(efficiencyMetrics.getEfficiencyPercentage(), 1, 1) + '%'}"
                               th:classappend="${efficiencyMetrics.isEfficient() ? 'text-success' : 'text-warning'}">0.0%</p>
                            <p class="card-text text-muted small">
                                <span th:text="${#numbers.formatDecimal(efficiencyMetrics.averageConsumptionPerHour(), 1, 2)}">0.00</span> kWh/hora promedio
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Consumption Patterns -->
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h6 class="card-subtitle mb-2 text-muted">
                                <i class="bi bi-graph-up me-1"></i>Patrón de Uso
                            </h6>
                            <p class="card-text h5" th:text="${consumptionPatterns.preferredChargingTime}">No data</p>
                            <p class="card-text text-muted small" th:text="${consumptionPatterns.getPatternDescription()}">
                                Sin patrón definido
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Always show charts when a spot is selected, regardless of data availability -->
    <div th:if="${selectedSpotId != null}">
        <!-- Combined Chart Card with Tabs using Thymeleaf Fragments -->
        <div class="card shadow-sm mb-4" id="chartCard">
            <div class="card-header py-2 py-md-2">
                <div class="row align-items-center">
                    <div class="col-12">
                        <ul class="nav nav-tabs nav-fill card-header-tabs flex-column flex-md-row" id="chartTabs" role="tablist">
                            <!-- Hourly Tab Button -->
                            <th:block th:replace="~{statistics/fragments/chart-fragments :: tab-button('hourly', 'Consumo por Hora', 'clock', true, ${isCurrentDay}, 'hora actual', 'HH:mm', false)}"></th:block>

                            <!-- Daily Tab Button -->
                            <th:block th:replace="~{statistics/fragments/chart-fragments :: tab-button('daily', 'Consumo Diario', 'calendar-week', false, ${isCurrentMonth}, 'día actual', 'dd/MM', false)}"></th:block>

                            <!-- Monthly Tab Button -->
                            <th:block th:replace="~{statistics/fragments/chart-fragments :: tab-button('monthly', 'Consumo Mensual', 'calendar3', false, ${isCurrentYear}, 'mes actual', 'MMM', true)}"></th:block>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body text-center py-0">
                <!-- Tab content container -->
                <div class="tab-content py-0 mb-0" id="chartTabsContent">
                    <!-- Hourly Chart Content -->
                    <th:block th:replace="~{statistics/fragments/chart-fragments :: chart-tab('hourly', 'Consumo por Hora', 'clock', ${hourlyBarChartSvg}, ${isCurrentDay}, 'hora actual', 'HH:mm', false)}"></th:block>

                    <!-- Daily Chart Content -->
                    <th:block th:replace="~{statistics/fragments/chart-fragments :: chart-tab('daily', 'Consumo Diario', 'calendar-week', ${dailyBarChartSvg}, ${isCurrentMonth}, 'día actual', 'dd/MM', false)}"></th:block>

                    <!-- Monthly Chart Content -->
                    <th:block th:replace="~{statistics/fragments/chart-fragments :: chart-tab('monthly', 'Consumo Mensual', 'calendar3', ${monthlyBarChartSvg}, ${isCurrentYear}, 'mes actual', 'MMM', true)}"></th:block>
                </div>
            </div>
            <div class="card-footer py-2">
                <th:block th:replace="~{statistics/fragments/filter-container :: filter-container}"></th:block>
            </div>
        </div>

        <!-- Detailed Analytics Section -->
        <div th:if="${selectedSpotId != null && utilizationMetrics != null}" class="card shadow-sm mb-4">
            <div class="card-header py-2">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up-arrow me-2"></i>Análisis Detallado
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Peak Hours Analysis -->
                    <div class="col-md-6 mb-4">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-clock me-2"></i>Horas Pico
                        </h6>
                        <div th:if="${peakHoursData != null && !peakHoursData.isEmpty()}">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center"
                                     th:each="entry, iterStat : ${peakHoursData}" th:if="${iterStat.index < 3}">
                                    <div>
                                        <span class="fw-bold" th:text="${entry.key}">Hora</span>
                                        <span class="badge bg-primary ms-2" th:text="'#' + ${iterStat.index + 1}">1</span>
                                    </div>
                                    <span class="text-muted" th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}">0.00 kWh</span>
                                </div>
                            </div>
                        </div>
                        <div th:if="${peakHoursData == null || peakHoursData.isEmpty()}" class="text-muted">
                            No hay datos de horas pico disponibles
                        </div>
                    </div>

                    <!-- Efficiency Details -->
                    <div class="col-md-6 mb-4">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-lightning-charge me-2"></i>Detalles de Eficiencia
                        </h6>
                        <div th:if="${efficiencyMetrics != null}">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="text-center p-2 border rounded">
                                        <div class="small text-muted">Consumo Pico</div>
                                        <div class="fw-bold" th:text="${#numbers.formatDecimal(efficiencyMetrics.peakConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 border rounded">
                                        <div class="small text-muted">Consumo Mínimo</div>
                                        <div class="fw-bold" th:text="${#numbers.formatDecimal(efficiencyMetrics.minimumConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 border rounded">
                                        <div class="small text-muted">Varianza</div>
                                        <div class="fw-bold"
                                             th:text="${#numbers.formatDecimal(efficiencyMetrics.consumptionVariance(), 1, 3)}"
                                             th:classappend="${efficiencyMetrics.hasStableConsumption() ? 'text-success' : 'text-warning'}">0.000</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-2 border rounded">
                                        <div class="small text-muted">Energía Desperdiciada</div>
                                        <div class="fw-bold text-danger" th:text="${#numbers.formatDecimal(efficiencyMetrics.totalEnergyWasted(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Consumption Pattern Analysis -->
                <div class="row" th:if="${consumptionPatterns != null}">
                    <div class="col-12">
                        <h6 class="fw-bold mb-3">
                            <i class="bi bi-calendar-week me-2"></i>Análisis de Patrones
                        </h6>
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <div class="small text-muted">Mañana</div>
                                    <div class="fw-bold" th:text="${#numbers.formatDecimal(consumptionPatterns.morningConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    <div class="small text-muted">06:00-12:00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <div class="small text-muted">Tarde</div>
                                    <div class="fw-bold" th:text="${#numbers.formatDecimal(consumptionPatterns.afternoonConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    <div class="small text-muted">12:00-18:00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <div class="small text-muted">Noche</div>
                                    <div class="fw-bold" th:text="${#numbers.formatDecimal(consumptionPatterns.eveningConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    <div class="small text-muted">18:00-24:00</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <div class="small text-muted">Madrugada</div>
                                    <div class="fw-bold" th:text="${#numbers.formatDecimal(consumptionPatterns.nightConsumption(), 1, 2) + ' kWh'}">0.00 kWh</div>
                                    <div class="small text-muted">00:00-06:00</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 p-3 bg-light rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Período Preferido:</strong> <span th:text="${consumptionPatterns.getPeakPeriod()}">No definido</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>Tipo de Uso:</strong>
                                    <span th:text="${consumptionPatterns.isWeekdayDominant() ? 'Principalmente días laborables' : 'Principalmente fines de semana'}">No definido</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Tables Section (Using List Groups) - Always show regardless of data availability -->
        <div class="row">
            <!-- Hourly Data List -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-list-ul me-3 align-self-center"></i>
                            <div>
                                <div>Consumos por hora</div>
                                <div class="small text-muted mt-1">
                                    (<span th:text="${#temporals.format(selectedDay, 'dd/MM/yyyy')}"></span>
                                    <span th:if="${isCurrentDay}">
                                        hasta <span th:text="${#temporals.format(#temporals.createNow(), 'HH:mm')}"></span>
                                    </span>)
                                </div>
                            </div>
                        </h5>
                    </div>
                    <div class="card-body p-0 overflow-auto" style="height: 250px;">
                        <!-- Show data list when data exists -->
                        <ul class="list-group list-group-flush" th:if="${hasHourlyData}">
                            <li class="list-group-item" th:each="entry : ${hourlyData}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <!-- Desktop view (inline) -->
                                        <div class="d-none d-md-block">
                                            <span class="fw-bold" th:text="${entry.key}"></span>&nbsp;
                                            <i class="bi bi-arrow-right">&nbsp;&nbsp;</i><span th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></span>
                                        </div>

                                        <!-- Mobile view (stacked) -->
                                        <div class="d-md-none">
                                            <div class="fw-bold" th:text="${entry.key}"></div>
                                            <div th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <!-- Show placeholder when no data exists -->
                        <div class="d-flex align-items-center justify-content-center h-100" th:if="${!hasHourlyData}">
                            <div class="text-center p-4">
                                <i class="bi bi-list-ul text-muted" style="font-size: 2.5rem;"></i>
                                <h6 class="mt-3 text-muted">No hay datos para el período seleccionado</h6>
                                <p class="text-muted small mb-0">Utilice los controles de navegación para ver otros períodos</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-8 mb-2 mb-md-0">
                                <div class="small text-muted">
                                    <i class="bi bi-calendar-range me-1"></i>
                                    <span th:text="${#temporals.format(dailyStartDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(dailyStartDateTime, 'HH:mm')}"></span>
                                    <i class="bi bi-arrow-right mx-1"></i>
                                    <span th:text="${#temporals.format(dailyEndDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(dailyEndDateTime, 'HH:mm')}"></span>
                                </div>
                            </div>
                            <div class="col-12 col-md-4 text-center text-md-end">
                                <a th:if="${selectedSpotId != null}" class="btn btn-sm btn-outline-primary w-100 w-md-auto"
                                   th:href="@{/readings(spotId=${selectedSpotId},
                                                    startDate=${dailyStartDateTime},
                                                    endDate=${dailyEndDateTime},
                                                    collapsed='false')}"
                                   title="Ver histórico de lecturas">
                                    <i class="bi bi-clock-history me-1"></i>Histórico
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Data List -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-list-ul me-3 align-self-center"></i>
                            <div>
                                <div>Consumos por día</div>
                                <div class="small text-muted mt-1">
                                    (<span th:with="yearMonth=${T(java.time.YearMonth).parse(selectedMonth)}"
                                          th:text="${yearMonth.getMonth().getDisplayName(T(java.time.format.TextStyle).FULL, T(java.util.Locale).forLanguageTag('es'))} + ' de ' + ${yearMonth.getYear()}"></span>
                                    <span th:if="${isCurrentMonth}">
                                        hasta <span th:text="${#temporals.format(#temporals.createNow(), 'dd/MM')}"></span>
                                    </span>)
                                </div>
                            </div>
                        </h5>
                    </div>
                    <div class="card-body p-0 overflow-auto" style="height: 250px;">
                        <!-- Show data list when data exists -->
                        <ul class="list-group list-group-flush" th:if="${hasDailyData}">
                            <li class="list-group-item" th:each="entry : ${dailyData}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <!-- Desktop view (inline) -->
                                        <div class="d-none d-md-block">
                                            <span class="fw-bold" th:text="${entry.key}"></span>&nbsp;
                                            <i class="bi bi-arrow-right">&nbsp;&nbsp;</i><span th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></span>
                                        </div>

                                        <!-- Mobile view (stacked) -->
                                        <div class="d-md-none">
                                            <div class="fw-bold" th:text="${entry.key}"></div>
                                            <div th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <!-- Show placeholder when no data exists -->
                        <div class="d-flex align-items-center justify-content-center h-100" th:if="${!hasDailyData}">
                            <div class="text-center p-4">
                                <i class="bi bi-list-ul text-muted" style="font-size: 2.5rem;"></i>
                                <h6 class="mt-3 text-muted">No hay datos para el período seleccionado</h6>
                                <p class="text-muted small mb-0">Utilice los controles de navegación para ver otros períodos</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-8 mb-2 mb-md-0">
                                <div class="small text-muted">
                                    <i class="bi bi-calendar-range me-1"></i>
                                    <span th:text="${#temporals.format(monthlyStartDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(monthlyStartDateTime, 'HH:mm')}"></span>
                                    <i class="bi bi-arrow-right mx-1"></i>
                                    <span th:text="${#temporals.format(monthlyEndDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(monthlyEndDateTime, 'HH:mm')}"></span>
                                </div>
                            </div>
                            <div class="col-12 col-md-4 text-center text-md-end">
                                <a th:if="${selectedSpotId != null}" class="btn btn-sm btn-outline-primary w-100 w-md-auto"
                                   th:href="@{/readings(spotId=${selectedSpotId},
                                                    startDate=${monthlyStartDateTime},
                                                    endDate=${monthlyEndDateTime},
                                                    collapsed='false')}"
                                   title="Ver histórico de lecturas">
                                    <i class="bi bi-clock-history me-1"></i>Histórico
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Data List -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header">
                        <h5 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-list-ul me-3 align-self-center"></i>
                            <div>
                                <div>Consumos por mes</div>
                                <div class="small text-muted mt-1">
                                    (Año <span th:text="${selectedYear}"></span>
                                    <span th:if="${isCurrentYear}">
                                        hasta <span th:text="${#temporals.format(#temporals.createNow(), 'MMM', new java.util.Locale('es'))}"></span>
                                    </span>)
                                </div>
                            </div>
                        </h5>
                    </div>
                    <div class="card-body p-0 overflow-auto" style="height: 250px;">
                        <!-- Show data list when data exists -->
                        <ul class="list-group list-group-flush" th:if="${hasMonthlyData}">
                            <li class="list-group-item" th:each="entry : ${monthlyData}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <!-- Desktop view (inline) -->
                                        <div class="d-none d-md-block">
                                            <span class="fw-bold" th:text="${entry.key}"></span>&nbsp;
                                            <i class="bi bi-arrow-right">&nbsp;&nbsp;</i><span th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></span>
                                        </div>

                                        <!-- Mobile view (stacked) -->
                                        <div class="d-md-none">
                                            <div class="fw-bold" th:text="${entry.key}"></div>
                                            <div th:text="${#numbers.formatDecimal(entry.value, 1, 2) + ' kWh'}"></div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <!-- Show placeholder when no data exists -->
                        <div class="d-flex align-items-center justify-content-center h-100" th:if="${!hasMonthlyData}">
                            <div class="text-center p-4">
                                <i class="bi bi-list-ul text-muted" style="font-size: 2.5rem;"></i>
                                <h6 class="mt-3 text-muted">No hay datos para el período seleccionado</h6>
                                <p class="text-muted small mb-0">Utilice los controles de navegación para ver otros períodos</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer py-2">
                        <div class="row align-items-center">
                            <div class="col-12 col-md-8 mb-2 mb-md-0">
                                <div class="small text-muted">
                                    <i class="bi bi-calendar-range me-1"></i>
                                    <span th:text="${#temporals.format(yearlyStartDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(yearlyStartDateTime, 'HH:mm')}"></span>
                                    <i class="bi bi-arrow-right mx-1"></i>
                                    <span th:text="${#temporals.format(yearlyEndDateTime, 'dd/MM/yyyy')}"></span>
                                    <span class="d-none d-md-inline" th:text="' ' + ${#temporals.format(yearlyEndDateTime, 'HH:mm')}"></span>
                                </div>
                            </div>
                            <div class="col-12 col-md-4 text-center text-md-end">
                                <a th:if="${selectedSpotId != null}" class="btn btn-sm btn-outline-primary w-100 w-md-auto"
                                   th:href="@{/readings(spotId=${selectedSpotId},
                                                    startDate=${yearlyStartDateTime},
                                                    endDate=${yearlyEndDateTime},
                                                    collapsed='false')}"
                                   title="Ver histórico de lecturas">
                                    <i class="bi bi-clock-history me-1"></i>Histórico
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    /* Minimal required chart styles */
    .chart-container {
        width: 100%;
        min-height: 350px;
        padding-bottom: 20px; /* Space for rotated labels */
    }

    .chart-container svg {
        max-width: 100%;
        height: auto;
    }

    /* Card header styling for two-line titles */
    .card-header .card-title i {
        font-size: 1.1rem;
    }

    .card-header .card-title > div {
        line-height: 1.3;
    }

    .card-header .card-title .small {
        font-size: 0.75rem;
        line-height: 1.2;
    }

    /* Card footer styling */
    .card-footer .small {
        font-size: 0.75rem;
        line-height: 1.4;
    }

    .card-footer .bi-calendar-range {
        color: #6c757d;
    }

    /* Filter container styling */
    #filter-container {
        width: 100% !important;
        margin: 0 !important;
    }

    #filter-container .input-group {
        width: 100% !important;
        display: flex;
        flex-wrap: nowrap;
    }

    #filter-container .input-group-text {
        flex: 0 0 auto;
        width: 40px;
        justify-content: center;
        padding: 0.375rem 0.5rem;
    }

    #filter-container .form-control {
        flex: 1;
    }

    #filter-container .input-group .btn {
        width: auto;
        flex-shrink: 0;
    }

    #hourly-filter, #daily-filter, #monthly-filter {
        width: 100% !important;
    }

    #unified-download {
        width: 100%;
    }

    @media (min-width: 768px) {
        #unified-download {
            width: auto;
        }
    }

    @media (max-width: 767.98px) {
        .card-footer .small {
            text-align: center;
        }
    }

    /* Tab styling for better mobile experience */
    .nav-tabs .nav-item {
        margin-bottom: 0;
    }

    .nav-tabs .nav-link {
        border-radius: 0;
        padding: 0.75rem 0.5rem;
        transition: all 0.2s ease;
    }

    /* On mobile, make tabs look like buttons stacked vertically */
    @media (max-width: 767.98px) {
        .nav-tabs {
            border-bottom: none;
        }

        .nav-tabs .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-tabs .nav-link {
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }

        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .chart-container {
            min-height: 250px;
        }

        .chart-container .axis-label {
            font-size: 7px;
        }

        /* Ensure navigation buttons and input-group-text stay properly sized on mobile */
        #filter-container .input-group .btn,
        #filter-container .input-group-text {
            width: 36px;
            min-width: 36px;
            max-width: 36px;
            padding-left: 0.375rem;
            padding-right: 0.375rem;
        }
    }

    /* Hide icons in very small screens */
    @media (max-width: 400px) {
        .nav-tabs .nav-link i {
            display: none;
        }
    }

    /* Navigation button styling */
    #filter-container .input-group .btn {
        min-width: 40px;
        max-width: 40px;
    }

    /* Calendar icon styling */
    #filter-container .input-group-text i {
        font-size: 1rem;
        margin: 0 auto;
    }

    /* Filtered totals styling */
    .alert-info {
        background-color: #e7f3ff;
        border-color: #b8daff;
        color: #004085;
    }

    .alert-info .fw-bold {
        color: #003d82;
    }

    /* Responsive adjustments for filtered totals */
    @media (max-width: 768px) {
        .alert-info .d-flex {
            flex-direction: column;
            align-items: flex-start !important;
        }

        .alert-info .text-muted {
            margin-top: 0.5rem;
        }
    }

</style>

<!-- Include toast notifications -->
<th:block th:replace="~{layout/toast-include :: toast-include}"></th:block>
</body>
</html>
