package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.dto.analytics.ConsumptionPatterns;
import com.hakcu.evmodbus.dto.analytics.EfficiencyMetrics;
import com.hakcu.evmodbus.dto.analytics.UtilizationMetrics;
import com.hakcu.evmodbus.entities.Reading;
import com.hakcu.evmodbus.dto.TimePeriodData;
import com.hakcu.evmodbus.repositories.ReadingRepository;
import com.hakcu.evmodbus.services.consumption.ConsumptionCalculationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for advanced analytics and data analysis of charging spot readings.
 */
@Service
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);
    private static final float CHARGING_THRESHOLD = 100.0f; // Watts
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    private final ReadingRepository readingRepository;
    private final ConsumptionCalculationService consumptionCalculationService;

    public AnalyticsService(ReadingRepository readingRepository,
                           ConsumptionCalculationService consumptionCalculationService) {
        this.readingRepository = readingRepository;
        this.consumptionCalculationService = consumptionCalculationService;
    }

    /**
     * Analyzes peak hours for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'peakHours_' + #spotId + '_' + #timePeriod.hashCode()")
    public Map<String, Float> getPeakHoursAnalysis(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Analyzing peak hours for spot {} in period {}", spotId, timePeriod);

        LocalDateTime start = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime end = timePeriod.isCurrentDay() ? LocalDateTime.now() :
                           timePeriod.selectedDay().atTime(LocalTime.MAX);

        Map<String, Float> hourlyConsumption = consumptionCalculationService.getHourlyConsumption(spotId, start, end);

        // Find peak hours (top 3)
        return hourlyConsumption.entrySet().stream()
                .sorted(Map.Entry.<String, Float>comparingByValue().reversed())
                .limit(3)
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (e1, e2) -> e1,
                    LinkedHashMap::new
                ));
    }

    /**
     * Calculates utilization metrics for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'utilization_' + #spotId + '_' + #timePeriod.hashCode()")
    public UtilizationMetrics getUtilizationMetrics(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Calculating utilization metrics for spot {} in period {}", spotId, timePeriod);

        LocalDateTime start = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime end = timePeriod.isCurrentDay() ? LocalDateTime.now() :
                           timePeriod.selectedDay().atTime(LocalTime.MAX);

        List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, start, end);

        if (readings.isEmpty()) {
            return UtilizationMetrics.empty();
        }

        // Calculate metrics
        long totalReadings = readings.size();
        long chargingReadings = readings.stream()
                .filter(r -> r.getReading() > CHARGING_THRESHOLD)
                .count();

        float utilizationRate = totalReadings > 0 ? (float) chargingReadings / totalReadings : 0.0f;

        // Find peak utilization
        Reading peakReading = readings.stream()
                .max(Comparator.comparing(Reading::getReading))
                .orElse(null);

        float peakUtilization = peakReading != null ? peakReading.getReading() : 0.0f;
        LocalDateTime peakTime = peakReading != null ? peakReading.getDateTime() : null;

        // Calculate session metrics
        int activeSessions = (int) chargingReadings;
        float averageSessionDuration = calculateAverageSessionDuration(readings);

        // Calculate time distribution
        float chargingTime = utilizationRate * 24; // Hours in a day
        float idleTime = 24 - chargingTime;

        // Calculate efficiency score
        float efficiencyScore = calculateEfficiencyScore(readings);

        return new UtilizationMetrics(
            utilizationRate,
            averageSessionDuration,
            (int) totalReadings,
            activeSessions,
            peakUtilization,
            peakTime,
            idleTime,
            chargingTime,
            efficiencyScore
        );
    }

    /**
     * Calculates efficiency metrics for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'efficiency_' + #spotId + '_' + #timePeriod.hashCode()")
    public EfficiencyMetrics getEfficiencyMetrics(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Calculating efficiency metrics for spot {} in period {}", spotId, timePeriod);

        LocalDateTime start = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime end = timePeriod.isCurrentDay() ? LocalDateTime.now() :
                           timePeriod.selectedDay().atTime(LocalTime.MAX);

        Map<String, Float> hourlyConsumption = consumptionCalculationService.getHourlyConsumption(spotId, start, end);

        if (hourlyConsumption.isEmpty()) {
            return EfficiencyMetrics.empty();
        }

        // Calculate basic metrics
        float totalConsumption = hourlyConsumption.values().stream().reduce(0.0f, Float::sum);
        float averageConsumption = totalConsumption / hourlyConsumption.size();

        // Find peak and minimum
        float peakConsumption = hourlyConsumption.values().stream().max(Float::compare).orElse(0.0f);
        float minConsumption = hourlyConsumption.values().stream().min(Float::compare).orElse(0.0f);

        // Calculate variance
        float variance = calculateVariance(hourlyConsumption.values(), averageConsumption);

        // Calculate efficiency ratio
        float efficiencyRatio = peakConsumption > 0 ? averageConsumption / peakConsumption : 0.0f;

        // Calculate energy waste (consumption below threshold)
        float energyWasted = hourlyConsumption.values().stream()
                .filter(consumption -> consumption < CHARGING_THRESHOLD / 1000) // Convert to kWh
                .reduce(0.0f, Float::sum);

        // Calculate optimal consumption rate
        float optimalRate = peakConsumption * 0.8f; // 80% of peak is considered optimal

        return new EfficiencyMetrics(
            averageConsumption,
            peakConsumption,
            null, // Peak time would need additional logic
            minConsumption,
            null, // Min time would need additional logic
            variance,
            efficiencyRatio,
            hourlyConsumption,
            energyWasted,
            optimalRate
        );
    }

    /**
     * Analyzes consumption patterns for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'patterns_' + #spotId + '_' + #timePeriod.hashCode()")
    public ConsumptionPatterns getConsumptionPatterns(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Analyzing consumption patterns for spot {} in period {}", spotId, timePeriod);

        // For now, analyze the selected month for patterns
        LocalDateTime monthStart = timePeriod.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime monthEnd = timePeriod.getYearMonth().atEndOfMonth().atTime(LocalTime.MAX);

        Map<String, Float> dailyConsumption = consumptionCalculationService.getDailyConsumption(spotId, monthStart, monthEnd);

        if (dailyConsumption.isEmpty()) {
            return ConsumptionPatterns.empty();
        }

        // Analyze time periods (simplified for now)
        float totalConsumption = dailyConsumption.values().stream().reduce(0.0f, Float::sum);
        float averageDaily = totalConsumption / dailyConsumption.size();

        // Simplified pattern analysis
        Map<String, Float> peakHours = getPeakHoursAnalysis(spotId, timePeriod);

        // Calculate period consumption (simplified)
        float morningConsumption = averageDaily * 0.25f;
        float afternoonConsumption = averageDaily * 0.35f;
        float eveningConsumption = averageDaily * 0.30f;
        float nightConsumption = averageDaily * 0.10f;

        // Determine preferred charging time
        String preferredTime = "Tarde"; // Simplified

        // Calculate weekday vs weekend (simplified)
        float weekdayAverage = averageDaily * 1.1f;
        float weekendAverage = averageDaily * 0.9f;

        boolean hasConsistentPattern = calculateVariance(dailyConsumption.values(), averageDaily) < 0.3f;

        return new ConsumptionPatterns(
            peakHours,
            Map.of(), // Weekly pattern would need more complex logic
            new ArrayList<>(peakHours.keySet()),
            List.of(),
            morningConsumption,
            afternoonConsumption,
            eveningConsumption,
            nightConsumption,
            preferredTime,
            weekdayAverage,
            weekendAverage,
            hasConsistentPattern
        );
    }

    // Helper methods
    private float calculateAverageSessionDuration(List<Reading> readings) {
        // Simplified calculation - would need more complex logic for actual sessions
        return readings.size() > 0 ? 2.5f : 0.0f; // Average 2.5 hours per session
    }

    private float calculateEfficiencyScore(List<Reading> readings) {
        if (readings.isEmpty()) return 0.0f;

        long chargingReadings = readings.stream()
                .filter(r -> r.getReading() > CHARGING_THRESHOLD)
                .count();

        return readings.size() > 0 ? (float) chargingReadings / readings.size() : 0.0f;
    }

    private float calculateVariance(Collection<Float> values, float mean) {
        if (values.isEmpty()) return 0.0f;

        double variance = values.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .average()
                .orElse(0.0);

        return (float) Math.sqrt(variance);
    }
}
