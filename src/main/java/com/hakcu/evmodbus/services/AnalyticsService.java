package com.hakcu.evmodbus.services;

import com.hakcu.evmodbus.config.CacheConfig;
import com.hakcu.evmodbus.dto.analytics.*;
import com.hakcu.evmodbus.entities.Reading;
import com.hakcu.evmodbus.dto.TimePeriodData;
import com.hakcu.evmodbus.repositories.ReadingRepository;
import com.hakcu.evmodbus.services.consumption.ConsumptionCalculationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for advanced analytics and data analysis of charging spot readings.
 */
@Service
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);
    private static final float CHARGING_THRESHOLD = 100.0f; // Watts
    private static final DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    private final ReadingRepository readingRepository;
    private final ConsumptionCalculationService consumptionCalculationService;

    public AnalyticsService(ReadingRepository readingRepository,
                           ConsumptionCalculationService consumptionCalculationService) {
        this.readingRepository = readingRepository;
        this.consumptionCalculationService = consumptionCalculationService;
    }

    /**
     * Analyzes peak hours for a charging spot using ALL historical data.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'peakHours_historical_' + #spotId")
    public Map<String, Float> getPeakHoursAnalysis(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Analyzing historical peak hours for spot {}", spotId);

        // Get ALL historical data for this spot
        List<Reading> allReadings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(
            spotId, LocalDateTime.of(2000, 1, 1, 0, 0), LocalDateTime.now());

        if (allReadings.isEmpty()) {
            return Map.of();
        }

        // Analyze hourly patterns across ALL historical data
        Map<Integer, List<Float>> hourlyConsumptionData = new HashMap<>();

        // Group readings by hour of day (0-23)
        for (Reading reading : allReadings) {
            int hour = reading.getDateTime().getHour();
            hourlyConsumptionData.computeIfAbsent(hour, k -> new ArrayList<>()).add(reading.getReading());
        }

        // Calculate average consumption for each hour across all historical data
        Map<String, Float> hourlyAverages = new LinkedHashMap<>();
        for (int hour = 0; hour < 24; hour++) {
            List<Float> readings = hourlyConsumptionData.get(hour);
            if (readings != null && !readings.isEmpty()) {
                float average = (float) readings.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
                hourlyAverages.put(String.format("%02d:00", hour), average);
            }
        }

        // Find peak hours (top 3) from historical averages
        return hourlyAverages.entrySet().stream()
                .sorted(Map.Entry.<String, Float>comparingByValue().reversed())
                .limit(3)
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (e1, e2) -> e1,
                    LinkedHashMap::new
                ));
    }

    /**
     * Calculates utilization metrics for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'utilization_' + #spotId + '_' + #timePeriod.hashCode()")
    public UtilizationMetrics getUtilizationMetrics(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Calculating utilization metrics for spot {} in period {}", spotId, timePeriod);

        LocalDateTime start = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime end = timePeriod.isCurrentDay() ? LocalDateTime.now() :
                           timePeriod.selectedDay().atTime(LocalTime.MAX);

        List<Reading> readings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(spotId, start, end);

        if (readings.isEmpty()) {
            return UtilizationMetrics.empty();
        }

        // Calculate metrics
        long totalReadings = readings.size();
        long chargingReadings = readings.stream()
                .filter(r -> r.getReading() > CHARGING_THRESHOLD)
                .count();

        float utilizationRate = totalReadings > 0 ? (float) chargingReadings / totalReadings : 0.0f;

        // Find peak utilization
        Reading peakReading = readings.stream()
                .max(Comparator.comparing(Reading::getReading))
                .orElse(null);

        float peakUtilization = peakReading != null ? peakReading.getReading() : 0.0f;
        LocalDateTime peakTime = peakReading != null ? peakReading.getDateTime() : null;

        // Calculate session metrics
        int activeSessions = (int) chargingReadings;
        float averageSessionDuration = calculateAverageSessionDuration(readings);

        // Calculate time distribution
        float chargingTime = utilizationRate * 24; // Hours in a day
        float idleTime = 24 - chargingTime;

        // Calculate efficiency score
        float efficiencyScore = calculateEfficiencyScore(readings);

        return new UtilizationMetrics(
            utilizationRate,
            averageSessionDuration,
            (int) totalReadings,
            activeSessions,
            peakUtilization,
            peakTime,
            idleTime,
            chargingTime,
            efficiencyScore
        );
    }

    /**
     * Calculates efficiency metrics for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'efficiency_' + #spotId + '_' + #timePeriod.hashCode()")
    public EfficiencyMetrics getEfficiencyMetrics(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Calculating efficiency metrics for spot {} in period {}", spotId, timePeriod);

        LocalDateTime start = timePeriod.selectedDay().atStartOfDay();
        LocalDateTime end = timePeriod.isCurrentDay() ? LocalDateTime.now() :
                           timePeriod.selectedDay().atTime(LocalTime.MAX);

        Map<String, Float> hourlyConsumption = consumptionCalculationService.getHourlyConsumption(spotId, start, end);

        if (hourlyConsumption.isEmpty()) {
            return EfficiencyMetrics.empty();
        }

        // Calculate basic metrics
        float totalConsumption = hourlyConsumption.values().stream().reduce(0.0f, Float::sum);
        float averageConsumption = totalConsumption / hourlyConsumption.size();

        // Find peak and minimum
        float peakConsumption = hourlyConsumption.values().stream().max(Float::compare).orElse(0.0f);
        float minConsumption = hourlyConsumption.values().stream().min(Float::compare).orElse(0.0f);

        // Calculate variance
        float variance = calculateVariance(hourlyConsumption.values(), averageConsumption);

        // Calculate efficiency ratio
        float efficiencyRatio = peakConsumption > 0 ? averageConsumption / peakConsumption : 0.0f;

        // Calculate energy waste (consumption below threshold)
        float energyWasted = hourlyConsumption.values().stream()
                .filter(consumption -> consumption < CHARGING_THRESHOLD / 1000) // Convert to kWh
                .reduce(0.0f, Float::sum);

        // Calculate optimal consumption rate
        float optimalRate = peakConsumption * 0.8f; // 80% of peak is considered optimal

        return new EfficiencyMetrics(
            averageConsumption,
            peakConsumption,
            null, // Peak time would need additional logic
            minConsumption,
            null, // Min time would need additional logic
            variance,
            efficiencyRatio,
            hourlyConsumption,
            energyWasted,
            optimalRate
        );
    }

    /**
     * Analyzes consumption patterns for a charging spot.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'patterns_' + #spotId + '_' + #timePeriod.hashCode()")
    public ConsumptionPatterns getConsumptionPatterns(Long spotId, TimePeriodData timePeriod) {
        logger.debug("Analyzing consumption patterns for spot {} in period {}", spotId, timePeriod);

        // For now, analyze the selected month for patterns
        LocalDateTime monthStart = timePeriod.getYearMonth().atDay(1).atStartOfDay();
        LocalDateTime monthEnd = timePeriod.getYearMonth().atEndOfMonth().atTime(LocalTime.MAX);

        Map<String, Float> dailyConsumption = consumptionCalculationService.getDailyConsumption(spotId, monthStart, monthEnd);

        if (dailyConsumption.isEmpty()) {
            return ConsumptionPatterns.empty();
        }

        // Analyze time periods (simplified for now)
        float totalConsumption = dailyConsumption.values().stream().reduce(0.0f, Float::sum);
        float averageDaily = totalConsumption / dailyConsumption.size();

        // Simplified pattern analysis
        Map<String, Float> peakHours = getPeakHoursAnalysis(spotId, timePeriod);

        // Calculate period consumption (simplified)
        float morningConsumption = averageDaily * 0.25f;
        float afternoonConsumption = averageDaily * 0.35f;
        float eveningConsumption = averageDaily * 0.30f;
        float nightConsumption = averageDaily * 0.10f;

        // Determine preferred charging time
        String preferredTime = "Tarde"; // Simplified

        // Calculate weekday vs weekend (simplified)
        float weekdayAverage = averageDaily * 1.1f;
        float weekendAverage = averageDaily * 0.9f;

        boolean hasConsistentPattern = calculateVariance(dailyConsumption.values(), averageDaily) < 0.3f;

        return new ConsumptionPatterns(
            peakHours,
            Map.of(), // Weekly pattern would need more complex logic
            new ArrayList<>(peakHours.keySet()),
            List.of(),
            morningConsumption,
            afternoonConsumption,
            eveningConsumption,
            nightConsumption,
            preferredTime,
            weekdayAverage,
            weekendAverage,
            hasConsistentPattern
        );
    }

    // Helper methods
    private float calculateAverageSessionDuration(List<Reading> readings) {
        // Simplified calculation - would need more complex logic for actual sessions
        return readings.size() > 0 ? 2.5f : 0.0f; // Average 2.5 hours per session
    }

    private float calculateEfficiencyScore(List<Reading> readings) {
        if (readings.isEmpty()) return 0.0f;

        long chargingReadings = readings.stream()
                .filter(r -> r.getReading() > CHARGING_THRESHOLD)
                .count();

        return readings.size() > 0 ? (float) chargingReadings / readings.size() : 0.0f;
    }

    private float calculateVariance(Collection<Float> values, float mean) {
        if (values.isEmpty()) return 0.0f;

        double variance = values.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .average()
                .orElse(0.0);

        return (float) Math.sqrt(variance);
    }

    /**
     * Generates comprehensive historical analytics for a charging spot.
     * Analyzes ALL available historical data regardless of current date filters.
     */
    @Cacheable(value = CacheConfig.ANALYTICS_CACHE, key = "'historical_analytics_' + #spotId")
    public HistoricalAnalytics getHistoricalAnalytics(Long spotId) {
        logger.debug("Generating comprehensive historical analytics for spot {}", spotId);

        // Get ALL historical data for this spot
        List<Reading> allReadings = readingRepository.findSuccessfulReadingsBySpotAndDateRange(
            spotId, LocalDateTime.of(2000, 1, 1, 0, 0), LocalDateTime.now());

        if (allReadings.isEmpty()) {
            return HistoricalAnalytics.empty();
        }

        // Sort readings by date
        allReadings.sort(Comparator.comparing(Reading::getDateTime));

        LocalDateTime dataRangeStart = allReadings.get(0).getDateTime();
        LocalDateTime dataRangeEnd = allReadings.get(allReadings.size() - 1).getDateTime();

        // Calculate basic metrics
        int totalDaysWithData = calculateDaysWithData(allReadings);
        float totalConsumption = calculateTotalHistoricalConsumption(allReadings);
        float averageDailyConsumption = totalDaysWithData > 0 ? totalConsumption / totalDaysWithData : 0.0f;
        float averageMonthlyConsumption = averageDailyConsumption * 30;

        // Generate multi-granular patterns
        Map<String, Float> hourlyPatterns = generateHourlyPatterns(allReadings);
        Map<String, Float> dailyPatterns = generateDailyPatterns(allReadings);
        Map<String, Float> monthlyPatterns = generateMonthlyPatterns(allReadings);
        Map<String, Float> yearlyTrends = generateYearlyTrends(allReadings);

        // Generate advanced analytics
        TrendAnalysis trendAnalysis = generateTrendAnalysis(allReadings);
        SessionAnalytics sessionAnalytics = generateSessionAnalytics(allReadings);
        SeasonalPatterns seasonalPatterns = generateSeasonalPatterns(allReadings);
        PerformanceMetrics performanceMetrics = generatePerformanceMetrics(allReadings);

        return new HistoricalAnalytics(
            dataRangeStart, dataRangeEnd, totalDaysWithData,
            totalConsumption, averageDailyConsumption, averageMonthlyConsumption,
            hourlyPatterns, dailyPatterns, monthlyPatterns, yearlyTrends,
            trendAnalysis, sessionAnalytics, seasonalPatterns, performanceMetrics
        );
    }

    // Helper methods for historical analysis
    private int calculateDaysWithData(List<Reading> readings) {
        return (int) readings.stream()
            .map(r -> r.getDateTime().toLocalDate())
            .distinct()
            .count();
    }

    private float calculateTotalHistoricalConsumption(List<Reading> readings) {
        if (readings.size() < 2) return 0.0f;

        float totalConsumption = 0.0f;
        Reading previousReading = null;

        for (Reading reading : readings) {
            if (previousReading != null) {
                float consumption = reading.getReading() - previousReading.getReading();
                if (consumption > 0) { // Only count positive consumption
                    totalConsumption += consumption;
                }
            }
            previousReading = reading;
        }

        return totalConsumption;
    }

    private Map<String, Float> generateHourlyPatterns(List<Reading> readings) {
        Map<Integer, List<Float>> hourlyData = new HashMap<>();

        for (Reading reading : readings) {
            int hour = reading.getDateTime().getHour();
            hourlyData.computeIfAbsent(hour, k -> new ArrayList<>()).add(reading.getReading());
        }

        Map<String, Float> patterns = new LinkedHashMap<>();
        for (int hour = 0; hour < 24; hour++) {
            List<Float> values = hourlyData.get(hour);
            if (values != null && !values.isEmpty()) {
                float average = (float) values.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
                patterns.put(String.format("%02d:00", hour), average);
            }
        }

        return patterns;
    }

    private Map<String, Float> generateDailyPatterns(List<Reading> readings) {
        Map<DayOfWeek, List<Float>> dailyData = new HashMap<>();

        for (Reading reading : readings) {
            DayOfWeek dayOfWeek = reading.getDateTime().getDayOfWeek();
            dailyData.computeIfAbsent(dayOfWeek, k -> new ArrayList<>()).add(reading.getReading());
        }

        Map<String, Float> patterns = new LinkedHashMap<>();
        for (DayOfWeek day : DayOfWeek.values()) {
            List<Float> values = dailyData.get(day);
            if (values != null && !values.isEmpty()) {
                float average = (float) values.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
                patterns.put(getDayName(day), average);
            }
        }

        return patterns;
    }

    private Map<String, Float> generateMonthlyPatterns(List<Reading> readings) {
        Map<Integer, List<Float>> monthlyData = new HashMap<>();

        for (Reading reading : readings) {
            int month = reading.getDateTime().getMonthValue();
            monthlyData.computeIfAbsent(month, k -> new ArrayList<>()).add(reading.getReading());
        }

        Map<String, Float> patterns = new LinkedHashMap<>();
        for (int month = 1; month <= 12; month++) {
            List<Float> values = monthlyData.get(month);
            if (values != null && !values.isEmpty()) {
                float average = (float) values.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
                patterns.put(getMonthName(month), average);
            }
        }

        return patterns;
    }

    private Map<String, Float> generateYearlyTrends(List<Reading> readings) {
        Map<Integer, List<Float>> yearlyData = new HashMap<>();

        for (Reading reading : readings) {
            int year = reading.getDateTime().getYear();
            yearlyData.computeIfAbsent(year, k -> new ArrayList<>()).add(reading.getReading());
        }

        Map<String, Float> trends = new LinkedHashMap<>();
        for (Integer year : yearlyData.keySet().stream().sorted().toList()) {
            List<Float> values = yearlyData.get(year);
            if (values != null && !values.isEmpty()) {
                float average = (float) values.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);
                trends.put(year.toString(), average);
            }
        }

        return trends;
    }

    private String getDayName(DayOfWeek day) {
        return switch (day) {
            case MONDAY -> "Lunes";
            case TUESDAY -> "Martes";
            case WEDNESDAY -> "Miércoles";
            case THURSDAY -> "Jueves";
            case FRIDAY -> "Viernes";
            case SATURDAY -> "Sábado";
            case SUNDAY -> "Domingo";
        };
    }

    private String getMonthName(int month) {
        return switch (month) {
            case 1 -> "Enero";
            case 2 -> "Febrero";
            case 3 -> "Marzo";
            case 4 -> "Abril";
            case 5 -> "Mayo";
            case 6 -> "Junio";
            case 7 -> "Julio";
            case 8 -> "Agosto";
            case 9 -> "Septiembre";
            case 10 -> "Octubre";
            case 11 -> "Noviembre";
            case 12 -> "Diciembre";
            default -> "Desconocido";
        };
    }

    private TrendAnalysis generateTrendAnalysis(List<Reading> readings) {
        if (readings.size() < 30) { // Need at least 30 readings for trend analysis
            return TrendAnalysis.empty();
        }

        // Calculate monthly consumption trends
        Map<String, Float> monthlyConsumption = new HashMap<>();
        Map<String, Integer> monthlyReadingCounts = new HashMap<>();

        for (Reading reading : readings) {
            String monthKey = reading.getDateTime().getYear() + "-" +
                            String.format("%02d", reading.getDateTime().getMonthValue());
            monthlyConsumption.merge(monthKey, reading.getReading(), Float::sum);
            monthlyReadingCounts.merge(monthKey, 1, Integer::sum);
        }

        // Calculate averages and trend
        List<Float> monthlyAverages = monthlyConsumption.entrySet().stream()
            .map(entry -> entry.getValue() / monthlyReadingCounts.get(entry.getKey()))
            .collect(Collectors.toList());

        if (monthlyAverages.size() < 3) {
            return TrendAnalysis.empty();
        }

        // Simple linear trend calculation
        float trendSlope = calculateTrendSlope(monthlyAverages);
        TrendAnalysis.TrendDirection direction = determineTrendDirection(trendSlope);
        float growthRate = Math.abs(trendSlope) / monthlyAverages.get(0);
        float volatility = calculateVariance(monthlyAverages,
            (float) monthlyAverages.stream().mapToDouble(Float::doubleValue).average().orElse(0.0));

        boolean isStable = volatility < 0.2f;
        float confidence = Math.min(0.95f, 1.0f - volatility);

        String description = generateTrendDescription(direction, growthRate, isStable);

        return new TrendAnalysis(direction, trendSlope, confidence, growthRate,
            monthlyAverages, volatility, isStable, description);
    }

    private float calculateTrendSlope(List<Float> values) {
        if (values.size() < 2) return 0.0f;

        float firstValue = values.get(0);
        float lastValue = values.get(values.size() - 1);

        return (lastValue - firstValue) / values.size();
    }

    private TrendAnalysis.TrendDirection determineTrendDirection(float slope) {
        if (Math.abs(slope) < 0.01f) return TrendAnalysis.TrendDirection.STABLE;
        return slope > 0 ? TrendAnalysis.TrendDirection.INCREASING : TrendAnalysis.TrendDirection.DECREASING;
    }

    private String generateTrendDescription(TrendAnalysis.TrendDirection direction, float growthRate, boolean isStable) {
        String stabilityText = isStable ? "con patrones estables" : "con alta variabilidad";

        return switch (direction) {
            case INCREASING -> String.format("Tendencia creciente (%.1f%% crecimiento) %s",
                growthRate * 100, stabilityText);
            case DECREASING -> String.format("Tendencia decreciente (%.1f%% reducción) %s",
                growthRate * 100, stabilityText);
            case STABLE -> String.format("Consumo estable %s", stabilityText);
            case INSUFFICIENT_DATA -> "Datos insuficientes para análisis de tendencias";
        };
    }

    private SessionAnalytics generateSessionAnalytics(List<Reading> readings) {
        if (readings.size() < 10) {
            return SessionAnalytics.empty();
        }

        // Detect charging sessions (readings > 100W for consecutive periods)
        List<ChargingSession> sessions = detectChargingSessions(readings);

        if (sessions.isEmpty()) {
            return SessionAnalytics.empty();
        }

        // Calculate session metrics
        int totalSessions = sessions.size();
        float averageDuration = (float) sessions.stream()
            .mapToDouble(ChargingSession::getDurationHours)
            .average().orElse(0.0);

        float medianDuration = calculateMedianDuration(sessions);
        float shortestSession = (float) sessions.stream()
            .mapToDouble(ChargingSession::getDurationHours)
            .min().orElse(0.0);
        float longestSession = (float) sessions.stream()
            .mapToDouble(ChargingSession::getDurationHours)
            .max().orElse(0.0);

        float averageConsumption = (float) sessions.stream()
            .mapToDouble(ChargingSession::getTotalConsumption)
            .average().orElse(0.0);

        // Calculate frequency metrics
        long totalDays = calculateDaysWithData(readings);
        int sessionsPerDay = totalDays > 0 ? (int) (totalSessions / (float) totalDays) : 0;
        int sessionsPerWeek = sessionsPerDay * 7;

        // Generate session patterns
        List<SessionAnalytics.SessionPattern> patterns = generateSessionPatterns(sessions);

        // Find most/least active days
        LocalDateTime mostActiveDay = findMostActiveDay(sessions);
        LocalDateTime leastActiveDay = findLeastActiveDay(sessions);

        float sessionEfficiency = calculateSessionEfficiency(sessions);

        return new SessionAnalytics(totalSessions, averageDuration, medianDuration,
            shortestSession, longestSession, averageConsumption, sessionsPerDay,
            sessionsPerWeek, patterns, mostActiveDay, leastActiveDay, sessionEfficiency);
    }

    // Helper class for session detection
    private static class ChargingSession {
        private final LocalDateTime start;
        private final LocalDateTime end;
        private final float totalConsumption;

        public ChargingSession(LocalDateTime start, LocalDateTime end, float totalConsumption) {
            this.start = start;
            this.end = end;
            this.totalConsumption = totalConsumption;
        }

        public double getDurationHours() {
            return java.time.Duration.between(start, end).toMinutes() / 60.0;
        }

        public float getTotalConsumption() {
            return totalConsumption;
        }

        public LocalDateTime getStart() { return start; }
        public LocalDateTime getEnd() { return end; }
    }

    private List<ChargingSession> detectChargingSessions(List<Reading> readings) {
        List<ChargingSession> sessions = new ArrayList<>();
        LocalDateTime sessionStart = null;
        float sessionStartReading = 0.0f;

        for (Reading reading : readings) {
            boolean isCharging = reading.getReading() > CHARGING_THRESHOLD;

            if (isCharging && sessionStart == null) {
                // Start of new session
                sessionStart = reading.getDateTime();
                sessionStartReading = reading.getReading();
            } else if (!isCharging && sessionStart != null) {
                // End of session
                float consumption = reading.getReading() - sessionStartReading;
                if (consumption > 0) {
                    sessions.add(new ChargingSession(sessionStart, reading.getDateTime(), consumption));
                }
                sessionStart = null;
            }
        }

        return sessions;
    }

    private float calculateMedianDuration(List<ChargingSession> sessions) {
        List<Double> durations = sessions.stream()
            .map(ChargingSession::getDurationHours)
            .sorted()
            .collect(Collectors.toList());

        int size = durations.size();
        if (size == 0) return 0.0f;
        if (size % 2 == 0) {
            return (float) (durations.get(size / 2 - 1) + durations.get(size / 2)) / 2;
        } else {
            return durations.get(size / 2).floatValue();
        }
    }

    private List<SessionAnalytics.SessionPattern> generateSessionPatterns(List<ChargingSession> sessions) {
        // Simplified pattern generation - could be enhanced
        Map<String, List<ChargingSession>> patterns = new HashMap<>();

        for (ChargingSession session : sessions) {
            int hour = session.getStart().getHour();
            String pattern;
            if (hour >= 6 && hour < 12) pattern = "Mañana";
            else if (hour >= 12 && hour < 18) pattern = "Tarde";
            else if (hour >= 18 && hour < 24) pattern = "Noche";
            else pattern = "Madrugada";

            patterns.computeIfAbsent(pattern, k -> new ArrayList<>()).add(session);
        }

        return patterns.entrySet().stream()
            .map(entry -> {
                List<ChargingSession> patternSessions = entry.getValue();
                float avgDuration = (float) patternSessions.stream()
                    .mapToDouble(ChargingSession::getDurationHours)
                    .average().orElse(0.0);
                float avgConsumption = (float) patternSessions.stream()
                    .mapToDouble(ChargingSession::getTotalConsumption)
                    .average().orElse(0.0);

                return new SessionAnalytics.SessionPattern(
                    entry.getKey(), patternSessions.size(), avgDuration, avgConsumption,
                    getPatternTimeDescription(entry.getKey())
                );
            })
            .collect(Collectors.toList());
    }

    private String getPatternTimeDescription(String pattern) {
        return switch (pattern) {
            case "Mañana" -> "06:00-12:00";
            case "Tarde" -> "12:00-18:00";
            case "Noche" -> "18:00-24:00";
            case "Madrugada" -> "00:00-06:00";
            default -> "Horario variable";
        };
    }

    private LocalDateTime findMostActiveDay(List<ChargingSession> sessions) {
        Map<LocalDateTime, Long> dailyCounts = sessions.stream()
            .collect(Collectors.groupingBy(
                s -> s.getStart().toLocalDate().atStartOfDay(),
                Collectors.counting()
            ));

        return dailyCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
    }

    private LocalDateTime findLeastActiveDay(List<ChargingSession> sessions) {
        Map<LocalDateTime, Long> dailyCounts = sessions.stream()
            .collect(Collectors.groupingBy(
                s -> s.getStart().toLocalDate().atStartOfDay(),
                Collectors.counting()
            ));

        return dailyCounts.entrySet().stream()
            .min(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(null);
    }

    private float calculateSessionEfficiency(List<ChargingSession> sessions) {
        if (sessions.isEmpty()) return 0.0f;

        double totalDuration = sessions.stream()
            .mapToDouble(ChargingSession::getDurationHours)
            .sum();

        double totalConsumption = sessions.stream()
            .mapToDouble(ChargingSession::getTotalConsumption)
            .sum();

        // Efficiency = consumption per hour
        return totalDuration > 0 ? (float) (totalConsumption / totalDuration) : 0.0f;
    }

    private SeasonalPatterns generateSeasonalPatterns(List<Reading> readings) {
        if (readings.size() < 100) { // Need sufficient data for seasonal analysis
            return SeasonalPatterns.empty();
        }

        // Group readings by season
        Map<String, List<Float>> seasonalData = new HashMap<>();
        Map<String, List<Float>> monthlyData = new HashMap<>();
        Map<String, List<Float>> weekdayWeekendData = new HashMap<>();

        for (Reading reading : readings) {
            String season = getSeason(reading.getDateTime().getMonthValue());
            String month = getMonthName(reading.getDateTime().getMonthValue());
            String dayType = reading.getDateTime().getDayOfWeek().getValue() <= 5 ? "weekday" : "weekend";

            seasonalData.computeIfAbsent(season, k -> new ArrayList<>()).add(reading.getReading());
            monthlyData.computeIfAbsent(month, k -> new ArrayList<>()).add(reading.getReading());
            weekdayWeekendData.computeIfAbsent(dayType, k -> new ArrayList<>()).add(reading.getReading());
        }

        // Calculate seasonal averages
        Map<String, Float> seasonalConsumption = seasonalData.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> (float) entry.getValue().stream().mapToDouble(Float::doubleValue).average().orElse(0.0)
            ));

        Map<String, Float> monthlyAverages = monthlyData.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> (float) entry.getValue().stream().mapToDouble(Float::doubleValue).average().orElse(0.0)
            ));

        Map<String, Float> weekdayVsWeekend = weekdayWeekendData.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> (float) entry.getValue().stream().mapToDouble(Float::doubleValue).average().orElse(0.0)
            ));

        // Find peak and low seasons
        String peakSeason = seasonalConsumption.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("No definido");

        String lowSeason = seasonalConsumption.entrySet().stream()
            .min(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse("No definido");

        // Calculate seasonal variation
        float maxSeasonal = seasonalConsumption.values().stream().max(Float::compare).orElse(0.0f);
        float minSeasonal = seasonalConsumption.values().stream().min(Float::compare).orElse(0.0f);
        float seasonalVariation = maxSeasonal > 0 ? (maxSeasonal - minSeasonal) / maxSeasonal : 0.0f;

        boolean hasSeasonalPattern = seasonalVariation > 0.15f; // 15% variation threshold

        String seasonalDescription = generateSeasonalDescription(peakSeason, lowSeason, seasonalVariation);

        return new SeasonalPatterns(seasonalConsumption, peakSeason, lowSeason, seasonalVariation,
            weekdayVsWeekend, monthlyAverages, hasSeasonalPattern, seasonalDescription);
    }

    private String getSeason(int month) {
        return switch (month) {
            case 12, 1, 2 -> "Invierno";
            case 3, 4, 5 -> "Primavera";
            case 6, 7, 8 -> "Verano";
            case 9, 10, 11 -> "Otoño";
            default -> "Desconocido";
        };
    }

    private String generateSeasonalDescription(String peakSeason, String lowSeason, float variation) {
        if (variation < 0.1f) {
            return "Consumo consistente durante todo el año";
        } else if (variation < 0.2f) {
            return String.format("Ligera variación estacional, mayor actividad en %s", peakSeason.toLowerCase());
        } else {
            return String.format("Marcada variación estacional: pico en %s, mínimo en %s",
                peakSeason.toLowerCase(), lowSeason.toLowerCase());
        }
    }

    private PerformanceMetrics generatePerformanceMetrics(List<Reading> readings) {
        if (readings.size() < 50) {
            return PerformanceMetrics.empty();
        }

        // Calculate utilization rate
        long chargingReadings = readings.stream()
            .filter(r -> r.getReading() > CHARGING_THRESHOLD)
            .count();
        float utilizationRate = (float) chargingReadings / readings.size();

        // Calculate peak demand and average power
        float peakDemand = readings.stream()
            .map(Reading::getReading)
            .max(Float::compare)
            .orElse(0.0f);

        float averagePower = (float) readings.stream()
            .mapToDouble(Reading::getReading)
            .average()
            .orElse(0.0);

        // Calculate energy efficiency (simplified)
        float energyEfficiency = utilizationRate * 0.8f; // Simplified calculation

        // Calculate uptime (assume 95% if we have readings)
        int uptimePercentage = 95;

        // Calculate performance score
        float performanceScore = (utilizationRate * 0.4f) + (energyEfficiency * 0.3f) +
                               (uptimePercentage / 100.0f * 0.3f);

        // Calculate consistency score
        float averageReading = (float) readings.stream()
            .mapToDouble(Reading::getReading)
            .average()
            .orElse(0.0);
        float variance = calculateVariance(
            readings.stream().map(Reading::getReading).collect(Collectors.toList()),
            averageReading
        );
        float consistencyScore = Math.max(0.0f, 1.0f - (variance / averageReading));

        // Determine performance grade
        String performanceGrade = calculatePerformanceGrade(performanceScore);

        // Find best and worst performance days (simplified)
        LocalDateTime bestDay = readings.stream()
            .max(Comparator.comparing(Reading::getReading))
            .map(Reading::getDateTime)
            .orElse(null);

        LocalDateTime worstDay = readings.stream()
            .min(Comparator.comparing(Reading::getReading))
            .map(Reading::getDateTime)
            .orElse(null);

        String performanceDescription = generatePerformanceDescription(performanceScore, utilizationRate);

        return new PerformanceMetrics(utilizationRate, peakDemand, averagePower, energyEfficiency,
            uptimePercentage, performanceScore, bestDay, worstDay, consistencyScore,
            performanceGrade, performanceDescription);
    }

    private String calculatePerformanceGrade(float score) {
        if (score >= 0.95f) return "A+";
        if (score >= 0.90f) return "A";
        if (score >= 0.85f) return "B+";
        if (score >= 0.80f) return "B";
        if (score >= 0.75f) return "C+";
        if (score >= 0.70f) return "C";
        if (score >= 0.60f) return "D";
        return "F";
    }

    private String generatePerformanceDescription(float score, float utilization) {
        if (score >= 0.9f) {
            return "Rendimiento excelente con alta eficiencia y utilización óptima";
        } else if (score >= 0.8f) {
            return "Buen rendimiento general con oportunidades de mejora menores";
        } else if (score >= 0.7f) {
            return "Rendimiento aceptable, se recomienda optimización";
        } else if (utilization < 0.3f) {
            return "Bajo rendimiento debido a subutilización del punto de carga";
        } else {
            return "Rendimiento deficiente, requiere atención y mejoras significativas";
        }
    }
}
