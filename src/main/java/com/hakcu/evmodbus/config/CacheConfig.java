package com.hakcu.evmodbus.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for caching.
 * Defines cache names and configuration.
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Cache names used in the application.
     * These constants can be used in @Cacheable and @CacheEvict annotations.
     */
    public static final String CUSTOMERS_CACHE = "customers";
    public static final String READINGS_CACHE = "readings";
    public static final String LATEST_READINGS_CACHE = "latestReadings";
    public static final String RECIPIENTS_CACHE = "recipients";
    public static final String SPOTS_CACHE = "spots";
    public static final String PAGINATED_READINGS_CACHE = "paginatedReadings";
    public static final String FILTERED_READINGS_CACHE = "filteredReadings";
    public static final String BILLING_RATES_CACHE = "billingRates";
    public static final String BILLINGS_CACHE = "billings";
    public static final String BILLING_ITEMS_CACHE = "billingItems";
    public static final String PAGINATED_BILLINGS_CACHE = "paginatedBillings";
    public static final String FILTERED_BILLINGS_CACHE = "filteredBillings";

    // Statistics caches
    public static final String HOURLY_CONSUMPTION_CACHE = "hourlyConsumption";
    public static final String DAILY_CONSUMPTION_CACHE = "dailyConsumption";
    public static final String MONTHLY_CONSUMPTION_CACHE = "monthlyConsumption";
    public static final String TOTAL_CONSUMPTION_CACHE = "totalConsumption";
    public static final String FORMATTED_DATES_CACHE = "formattedDates";
    public static final String LINE_CHARTS_CACHE = "lineCharts";
    public static final String BAR_CHARTS_CACHE = "barCharts";
    public static final String PIE_CHARTS_CACHE = "pieCharts";

    // Analytics caches
    public static final String ANALYTICS_CACHE = "analytics";

    /**
     * Configures the cache manager with the cache names.
     * Uses Spring's default ConcurrentMapCacheManager for simplicity.
     *
     * @return The configured cache manager
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        cacheManager.setCacheNames(java.util.Arrays.asList(
                // Entity caches
                CUSTOMERS_CACHE,
                READINGS_CACHE,
                LATEST_READINGS_CACHE,
                RECIPIENTS_CACHE,
                SPOTS_CACHE,
                PAGINATED_READINGS_CACHE,
                FILTERED_READINGS_CACHE,
                BILLING_RATES_CACHE,
                BILLINGS_CACHE,
                BILLING_ITEMS_CACHE,
                PAGINATED_BILLINGS_CACHE,
                FILTERED_BILLINGS_CACHE,

                // Statistics caches
                HOURLY_CONSUMPTION_CACHE,
                DAILY_CONSUMPTION_CACHE,
                MONTHLY_CONSUMPTION_CACHE,
                TOTAL_CONSUMPTION_CACHE,
                FORMATTED_DATES_CACHE,
                LINE_CHARTS_CACHE,
                BAR_CHARTS_CACHE,
                PIE_CHARTS_CACHE,

                // Analytics caches
                ANALYTICS_CACHE
        ));
        return cacheManager;
    }
}
