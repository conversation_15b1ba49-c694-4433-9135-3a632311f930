package com.hakcu.evmodbus.dto.analytics;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO representing charging session analytics based on historical data.
 */
public record SessionAnalytics(
    int totalSessions,
    float averageSessionDuration,
    float medianSessionDuration,
    float shortestSession,
    float longestSession,
    float averageSessionConsumption,
    int sessionsPerDay,
    int sessionsPerWeek,
    List<SessionPattern> sessionPatterns,
    LocalDateTime mostActiveDay,
    LocalDateTime leastActiveDay,
    float sessionEfficiency
) {
    
    /**
     * Represents a session pattern (e.g., morning sessions, weekend sessions).
     */
    public record SessionPattern(
        String patternName,
        int sessionCount,
        float averageDuration,
        float averageConsumption,
        String timeDescription
    ) {}
    
    /**
     * Creates empty session analytics.
     */
    public static SessionAnalytics empty() {
        return new SessionAnalytics(
            0, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0, 0,
            List.of(), null, null, 0.0f
        );
    }
    
    /**
     * Gets the average session duration in hours and minutes format.
     */
    public String getFormattedAverageSessionDuration() {
        if (averageSessionDuration <= 0) return "0h 0m";
        
        int hours = (int) averageSessionDuration;
        int minutes = (int) ((averageSessionDuration - hours) * 60);
        return String.format("%dh %dm", hours, minutes);
    }
    
    /**
     * Gets the session efficiency as a percentage.
     */
    public float getSessionEfficiencyPercentage() {
        return sessionEfficiency * 100;
    }
    
    /**
     * Checks if the spot has good session utilization (>= 5 sessions per week).
     */
    public boolean hasGoodSessionUtilization() {
        return sessionsPerWeek >= 5;
    }
    
    /**
     * Gets the session frequency description.
     */
    public String getSessionFrequencyDescription() {
        if (sessionsPerDay >= 3) return "Uso muy frecuente";
        if (sessionsPerDay >= 1) return "Uso frecuente";
        if (sessionsPerWeek >= 3) return "Uso moderado";
        if (sessionsPerWeek >= 1) return "Uso ocasional";
        return "Uso muy esporádico";
    }
    
    /**
     * Checks if there are sufficient sessions for meaningful analysis.
     */
    public boolean hasSufficientSessions() {
        return totalSessions >= 10;
    }
}
