package com.hakcu.evmodbus.dto.analytics;

import java.time.LocalDateTime;

/**
 * DTO representing utilization metrics for a charging spot.
 */
public record UtilizationMetrics(
    float utilizationRate,
    float averageSessionDuration,
    int totalSessions,
    int activeSessions,
    float peakUtilization,
    LocalDateTime peakUtilizationTime,
    float idleTime,
    float chargingTime,
    float efficiencyScore
) {
    
    /**
     * Creates utilization metrics with default values.
     */
    public static UtilizationMetrics empty() {
        return new UtilizationMetrics(
            0.0f, 0.0f, 0, 0, 0.0f, null, 0.0f, 0.0f, 0.0f
        );
    }
    
    /**
     * Gets the utilization rate as a percentage.
     */
    public float getUtilizationPercentage() {
        return utilizationRate * 100;
    }
    
    /**
     * Gets the efficiency score as a percentage.
     */
    public float getEfficiencyPercentage() {
        return efficiencyScore * 100;
    }
    
    /**
     * Checks if the spot has good utilization (>= 70%).
     */
    public boolean hasGoodUtilization() {
        return utilizationRate >= 0.7f;
    }
    
    /**
     * Checks if the spot has high efficiency (>= 80%).
     */
    public boolean hasHighEfficiency() {
        return efficiencyScore >= 0.8f;
    }
}
