package com.hakcu.evmodbus.dto.analytics;

import java.util.Map;

/**
 * DTO representing seasonal consumption patterns and trends.
 */
public record SeasonalPatterns(
    Map<String, Float> seasonalConsumption,
    String peakSeason,
    String lowSeason,
    float seasonalVariation,
    Map<String, Float> weekdayVsWeekend,
    Map<String, Float> monthlyAverages,
    boolean hasSeasonalPattern,
    String seasonalDescription
) {
    
    /**
     * Creates empty seasonal patterns.
     */
    public static SeasonalPatterns empty() {
        return new SeasonalPatterns(
            Map.of(), "No definido", "No definido", 0.0f,
            Map.of(), Map.of(), false, "Sin patrones estacionales detectados"
        );
    }
    
    /**
     * Gets the seasonal variation as a percentage.
     */
    public float getSeasonalVariationPercentage() {
        return seasonalVariation * 100;
    }
    
    /**
     * Checks if there's significant seasonal variation (>= 20%).
     */
    public boolean hasSignificantSeasonalVariation() {
        return seasonalVariation >= 0.2f;
    }
    
    /**
     * Gets the weekday vs weekend preference.
     */
    public String getWeekdayWeekendPreference() {
        if (weekdayVsWeekend.isEmpty()) return "No definido";
        
        float weekdayAvg = weekdayVsWeekend.getOrDefault("weekday", 0.0f);
        float weekendAvg = weekdayVsWeekend.getOrDefault("weekend", 0.0f);
        
        if (weekdayAvg > weekendAvg * 1.2f) return "Preferencia días laborables";
        if (weekendAvg > weekdayAvg * 1.2f) return "Preferencia fines de semana";
        return "Uso equilibrado";
    }
    
    /**
     * Gets the most active season description.
     */
    public String getMostActiveSeasonDescription() {
        if (!hasSeasonalPattern) return "Sin patrón estacional claro";
        return String.format("Mayor actividad en %s, menor en %s", peakSeason.toLowerCase(), lowSeason.toLowerCase());
    }
    
    /**
     * Gets the seasonal consistency description.
     */
    public String getSeasonalConsistencyDescription() {
        if (seasonalVariation < 0.1f) return "Consumo muy consistente durante el año";
        if (seasonalVariation < 0.2f) return "Consumo moderadamente consistente";
        if (seasonalVariation < 0.4f) return "Variación estacional notable";
        return "Alta variación estacional";
    }
}
