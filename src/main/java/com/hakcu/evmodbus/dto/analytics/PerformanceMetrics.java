package com.hakcu.evmodbus.dto.analytics;

import java.time.LocalDateTime;

/**
 * DTO representing performance metrics and benchmarks for a charging spot.
 */
public record PerformanceMetrics(
    float overallUtilizationRate,
    float peakDemandCapacity,
    float averageChargingPower,
    float energyEfficiencyRating,
    int uptimePercentage,
    float performanceScore,
    LocalDateTime bestPerformanceDay,
    LocalDateTime worstPerformanceDay,
    float consistencyScore,
    String performanceGrade,
    String performanceDescription
) {
    
    /**
     * Creates empty performance metrics.
     */
    public static PerformanceMetrics empty() {
        return new PerformanceMetrics(
            0.0f, 0.0f, 0.0f, 0.0f, 0, 0.0f,
            null, null, 0.0f, "N/A", "Sin datos suficientes para evaluación"
        );
    }
    
    /**
     * Gets the utilization rate as a percentage.
     */
    public float getUtilizationPercentage() {
        return overallUtilizationRate * 100;
    }
    
    /**
     * Gets the energy efficiency rating as a percentage.
     */
    public float getEfficiencyPercentage() {
        return energyEfficiencyRating * 100;
    }
    
    /**
     * Gets the performance score as a percentage.
     */
    public float getPerformanceScorePercentage() {
        return performanceScore * 100;
    }
    
    /**
     * Gets the consistency score as a percentage.
     */
    public float getConsistencyPercentage() {
        return consistencyScore * 100;
    }
    
    /**
     * Checks if the spot has excellent performance (score >= 90%).
     */
    public boolean hasExcellentPerformance() {
        return performanceScore >= 0.9f;
    }
    
    /**
     * Checks if the spot has good performance (score >= 70%).
     */
    public boolean hasGoodPerformance() {
        return performanceScore >= 0.7f;
    }
    
    /**
     * Gets the performance grade color for UI display.
     */
    public String getPerformanceGradeColor() {
        return switch (performanceGrade) {
            case "A", "A+" -> "success";
            case "B", "B+" -> "primary";
            case "C", "C+" -> "warning";
            case "D", "F" -> "danger";
            default -> "secondary";
        };
    }
    
    /**
     * Gets a detailed performance summary.
     */
    public String getPerformanceSummary() {
        if (performanceScore == 0.0f) return performanceDescription;
        
        return String.format("Puntuación general: %.1f%% (Grado %s) - %s", 
            getPerformanceScorePercentage(), performanceGrade, performanceDescription);
    }
}
