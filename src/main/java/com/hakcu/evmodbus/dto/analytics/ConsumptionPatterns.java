package com.hakcu.evmodbus.dto.analytics;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;

/**
 * DTO representing consumption patterns for a charging spot.
 */
public record ConsumptionPatterns(
    Map<String, Float> peakHours,
    Map<DayOfWeek, Float> weeklyPattern,
    List<String> mostActiveHours,
    List<String> leastActiveHours,
    float morningConsumption,
    float afternoonConsumption,
    float eveningConsumption,
    float nightConsumption,
    String preferredChargingTime,
    float weekdayAverage,
    float weekendAverage,
    boolean hasConsistentPattern
) {
    
    /**
     * Creates consumption patterns with default values.
     */
    public static ConsumptionPatterns empty() {
        return new ConsumptionPatterns(
            Map.of(), Map.of(), List.of(), List.of(),
            0.0f, 0.0f, 0.0f, 0.0f, "No data",
            0.0f, 0.0f, false
        );
    }
    
    /**
     * Gets the time period with highest consumption.
     */
    public String getPeakPeriod() {
        float max = Math.max(Math.max(morningConsumption, afternoonConsumption),
                           Math.max(eveningConsumption, nightConsumption));
        
        if (max == morningConsumption) return "Mañana (06:00-12:00)";
        if (max == afternoonConsumption) return "Tarde (12:00-18:00)";
        if (max == eveningConsumption) return "Noche (18:00-24:00)";
        return "Madrugada (00:00-06:00)";
    }
    
    /**
     * Checks if weekday usage is higher than weekend.
     */
    public boolean isWeekdayDominant() {
        return weekdayAverage > weekendAverage;
    }
    
    /**
     * Gets the consumption difference between weekdays and weekends.
     */
    public float getWeekdayWeekendDifference() {
        return Math.abs(weekdayAverage - weekendAverage);
    }
    
    /**
     * Gets a description of the charging pattern.
     */
    public String getPatternDescription() {
        if (!hasConsistentPattern) {
            return "Patrón irregular de consumo";
        }
        
        String peakPeriod = getPeakPeriod();
        String dayType = isWeekdayDominant() ? "días laborables" : "fines de semana";
        
        return String.format("Mayor actividad en %s, especialmente en %s", peakPeriod.toLowerCase(), dayType);
    }
}
