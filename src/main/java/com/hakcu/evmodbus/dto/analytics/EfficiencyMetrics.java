package com.hakcu.evmodbus.dto.analytics;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO representing efficiency metrics for a charging spot.
 */
public record EfficiencyMetrics(
    float averageConsumptionPerHour,
    float peakConsumption,
    LocalDateTime peakConsumptionTime,
    float minimumConsumption,
    LocalDateTime minimumConsumptionTime,
    float consumptionVariance,
    float energyEfficiencyRatio,
    Map<String, Float> hourlyEfficiency,
    float totalEnergyWasted,
    float optimalConsumptionRate
) {
    
    /**
     * Creates efficiency metrics with default values.
     */
    public static EfficiencyMetrics empty() {
        return new EfficiencyMetrics(
            0.0f, 0.0f, null, 0.0f, null, 0.0f, 0.0f, 
            Map.of(), 0.0f, 0.0f
        );
    }
    
    /**
     * Gets the efficiency ratio as a percentage.
     */
    public float getEfficiencyPercentage() {
        return energyEfficiencyRatio * 100;
    }
    
    /**
     * Checks if the consumption pattern is stable (low variance).
     */
    public boolean hasStableConsumption() {
        return consumptionVariance < 0.3f;
    }
    
    /**
     * Gets the energy waste percentage.
     */
    public float getEnergyWastePercentage() {
        if (peakConsumption == 0) return 0.0f;
        return (totalEnergyWasted / peakConsumption) * 100;
    }
    
    /**
     * Checks if the spot operates efficiently (>= 75% efficiency).
     */
    public boolean isEfficient() {
        return energyEfficiencyRatio >= 0.75f;
    }
}
