package com.hakcu.evmodbus.dto.analytics;

import java.util.List;

/**
 * DTO representing trend analysis for consumption patterns over time.
 */
public record TrendAnalysis(
    TrendDirection overallTrend,
    float trendSlope,
    float trendConfidence,
    float growthRate,
    List<Float> monthlyGrowthRates,
    float volatility,
    boolean isStable,
    String trendDescription
) {
    
    public enum TrendDirection {
        INCREASING, DECREASING, STABLE, INSUFFICIENT_DATA
    }
    
    /**
     * Creates empty trend analysis.
     */
    public static TrendAnalysis empty() {
        return new TrendAnalysis(
            TrendDirection.INSUFFICIENT_DATA,
            0.0f, 0.0f, 0.0f, List.of(), 0.0f, false,
            "Datos insuficientes para análisis de tendencias"
        );
    }
    
    /**
     * Gets the trend direction as a user-friendly string.
     */
    public String getTrendDirectionText() {
        return switch (overallTrend) {
            case INCREASING -> "Creciente";
            case DECREASING -> "Decreciente";
            case STABLE -> "Estable";
            case INSUFFICIENT_DATA -> "Sin datos suficientes";
        };
    }
    
    /**
     * Gets the growth rate as a percentage.
     */
    public float getGrowthRatePercentage() {
        return growthRate * 100;
    }
    
    /**
     * Checks if the trend is positive (increasing consumption).
     */
    public boolean isPositiveTrend() {
        return overallTrend == TrendDirection.INCREASING && growthRate > 0;
    }
    
    /**
     * Gets the confidence level as a percentage.
     */
    public float getConfidencePercentage() {
        return trendConfidence * 100;
    }
    
    /**
     * Checks if the trend analysis is reliable (confidence > 70%).
     */
    public boolean isReliable() {
        return trendConfidence > 0.7f;
    }
}
