package com.hakcu.evmodbus.dto.analytics;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO representing comprehensive historical analytics for a charging spot.
 * Contains analysis of ALL available historical data regardless of current date filters.
 */
public record HistoricalAnalytics(
    LocalDateTime dataRangeStart,
    LocalDateTime dataRangeEnd,
    int totalDaysWithData,
    float totalHistoricalConsumption,
    float averageDailyConsumption,
    float averageMonthlyConsumption,
    Map<String, Float> hourlyPatterns,
    Map<String, Float> dailyPatterns,
    Map<String, Float> monthlyPatterns,
    Map<String, Float> yearlyTrends,
    TrendAnalysis consumptionTrend,
    SessionAnalytics sessionAnalytics,
    SeasonalPatterns seasonalPatterns,
    PerformanceMetrics performanceMetrics
) {
    
    /**
     * Creates empty historical analytics.
     */
    public static HistoricalAnalytics empty() {
        return new HistoricalAnalytics(
            null, null, 0, 0.0f, 0.0f, 0.0f,
            Map.of(), Map.of(), Map.of(), Map.of(),
            TrendAnalysis.empty(),
            SessionAnalytics.empty(),
            SeasonalPatterns.empty(),
            PerformanceMetrics.empty()
        );
    }
    
    /**
     * Gets the data collection period in days.
     */
    public long getDataCollectionPeriodDays() {
        if (dataRangeStart == null || dataRangeEnd == null) return 0;
        return java.time.temporal.ChronoUnit.DAYS.between(dataRangeStart.toLocalDate(), dataRangeEnd.toLocalDate());
    }
    
    /**
     * Gets the data coverage percentage (days with data vs total days).
     */
    public float getDataCoveragePercentage() {
        long totalDays = getDataCollectionPeriodDays();
        if (totalDays == 0) return 0.0f;
        return (float) totalDaysWithData / totalDays * 100;
    }
    
    /**
     * Checks if there's sufficient data for meaningful analysis (at least 30 days).
     */
    public boolean hasSufficientData() {
        return totalDaysWithData >= 30;
    }
    
    /**
     * Gets a summary description of the historical data.
     */
    public String getDataSummary() {
        if (!hasSufficientData()) {
            return "Datos insuficientes para análisis histórico completo";
        }
        
        long totalDays = getDataCollectionPeriodDays();
        return String.format("Análisis de %d días (%d con datos, %.1f%% cobertura)", 
            totalDays, totalDaysWithData, getDataCoveragePercentage());
    }
}
