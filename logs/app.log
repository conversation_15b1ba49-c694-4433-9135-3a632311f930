2025-05-28T11:53:17.527+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 6272 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-28T11:53:17.536+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-28T11:53:17.587+02:00  INFO 6272 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-28T11:53:17.587+02:00  INFO 6272 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-28T11:53:18.392+02:00  INFO 6272 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28T11:53:18.500+02:00  INFO 6272 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 94 ms. Found 10 JPA repository interfaces.
2025-05-28T11:53:19.127+02:00  INFO 6272 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-28T11:53:19.142+02:00  INFO 6272 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-28T11:53:19.142+02:00  INFO 6272 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-28T11:53:19.187+02:00  INFO 6272 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-28T11:53:19.187+02:00  INFO 6272 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1600 ms
2025-05-28T11:53:19.414+02:00  INFO 6272 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28T11:53:19.472+02:00  INFO 6272 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-28T11:53:19.506+02:00  INFO 6272 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-28T11:53:19.763+02:00  INFO 6272 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-28T11:53:19.796+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-28T11:53:20.165+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@74a3764d
2025-05-28T11:53:20.167+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-28T11:53:20.228+02:00  INFO 6272 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-28T11:53:21.237+02:00  INFO 6272 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-28T11:53:22.896+02:00  INFO 6272 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T11:53:22.937+02:00  WARN 6272 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-28T11:53:23.177+02:00  INFO 6272 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-28T11:53:24.977+02:00  WARN 6272 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-28T11:53:25.029+02:00  INFO 6272 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-28T11:53:25.285+02:00  INFO 6272 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-28T11:53:25.797+02:00  INFO 6272 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-28T11:53:25.827+02:00  INFO 6272 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-28T11:53:25.842+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 8.691 seconds (process running for 9.756)
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-28T12:16:41.010+02:00  WARN 6272 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:41.010+02:00  WARN 6272 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:44.603+02:00  WARN 6272 --- [http-nio-8080-exec-9] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:44.603+02:00  WARN 6272 --- [http-nio-8080-exec-9] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:20:45.683+02:00  INFO 6272 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-28T12:20:45.683+02:00  INFO 6272 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-28T13:13:55.835+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15052 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-28T13:13:55.837+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-28T13:13:55.875+02:00  INFO 15052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-28T13:13:55.875+02:00  INFO 15052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-28T13:13:56.518+02:00  INFO 15052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28T13:13:56.595+02:00  INFO 15052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 70 ms. Found 10 JPA repository interfaces.
2025-05-28T13:13:57.102+02:00  INFO 15052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-28T13:13:57.113+02:00  INFO 15052 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-28T13:13:57.113+02:00  INFO 15052 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-28T13:13:57.154+02:00  INFO 15052 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-28T13:13:57.155+02:00  INFO 15052 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1280 ms
2025-05-28T13:13:57.331+02:00  INFO 15052 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28T13:13:57.387+02:00  INFO 15052 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-28T13:13:57.414+02:00  INFO 15052 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-28T13:13:57.648+02:00  INFO 15052 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-28T13:13:57.674+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-28T13:13:57.954+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@726ca1a3
2025-05-28T13:13:57.955+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-28T13:13:58.013+02:00  INFO 15052 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-28T13:13:58.869+02:00  INFO 15052 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-28T13:14:00.515+02:00  INFO 15052 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:14:00.551+02:00  WARN 15052 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-28T13:14:00.755+02:00  INFO 15052 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-28T13:14:02.399+02:00  WARN 15052 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-28T13:14:02.439+02:00  INFO 15052 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-28T13:14:02.657+02:00  INFO 15052 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-28T13:14:03.093+02:00  INFO 15052 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-28T13:14:03.132+02:00  INFO 15052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-28T13:14:03.139+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 7.673 seconds (process running for 8.546)
2025-05-28T13:14:11.599+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28T13:14:11.599+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-28T13:14:11.600+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-28T13:14:15.139+02:00  WARN 15052 --- [http-nio-8080-exec-10] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T13:14:15.139+02:00  WARN 15052 --- [http-nio-8080-exec-10] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T13:14:21.453+02:00  INFO 15052 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-28T13:14:21.453+02:00  INFO 15052 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-28T13:14:33.421+02:00  INFO 20960 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 20960 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-28T13:14:33.423+02:00  INFO 20960 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-28T13:14:33.461+02:00  INFO 20960 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-28T13:14:33.462+02:00  INFO 20960 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-28T13:14:34.083+02:00  INFO 20960 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28T13:14:34.158+02:00  INFO 20960 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 68 ms. Found 10 JPA repository interfaces.
2025-05-28T13:14:34.670+02:00  INFO 20960 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-28T13:14:34.680+02:00  INFO 20960 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-28T13:14:34.680+02:00  INFO 20960 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-28T13:14:34.721+02:00  INFO 20960 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-28T13:14:34.722+02:00  INFO 20960 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1259 ms
2025-05-28T13:14:34.916+02:00  INFO 20960 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28T13:14:34.964+02:00  INFO 20960 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-28T13:14:34.991+02:00  INFO 20960 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-28T13:14:35.225+02:00  INFO 20960 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-28T13:14:35.250+02:00  INFO 20960 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-28T13:14:35.797+02:00  INFO 20960 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5a714643
2025-05-28T13:14:35.799+02:00  INFO 20960 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-28T13:14:35.969+02:00  INFO 20960 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-28T13:14:36.876+02:00  INFO 20960 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-28T13:14:38.327+02:00  INFO 20960 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:14:38.358+02:00  WARN 20960 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-28T13:14:38.556+02:00  INFO 20960 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-28T13:14:40.121+02:00  WARN 20960 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-28T13:14:40.151+02:00  INFO 20960 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-28T13:14:40.361+02:00  INFO 20960 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-28T13:14:40.809+02:00  INFO 20960 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-28T13:14:40.843+02:00  INFO 20960 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-28T13:14:40.850+02:00  INFO 20960 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 7.82 seconds (process running for 8.483)
2025-05-28T13:14:52.701+02:00  INFO 20960 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28T13:14:52.702+02:00  INFO 20960 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-28T13:14:52.703+02:00  INFO 20960 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-28T13:14:55.003+02:00  INFO 20960 --- [Thread-8] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.003+02:00 ERROR 20960 --- [Thread-8] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@497ea400: Master is null.
2025-05-28T13:14:55.229+02:00  INFO 20960 --- [Thread-9] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.229+02:00 ERROR 20960 --- [Thread-9] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@26e0021c: Master is null.
2025-05-28T13:14:55.313+02:00  INFO 20960 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.313+02:00 ERROR 20960 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@6d2556d5: Master is null.
2025-05-28T13:14:55.366+02:00  INFO 20960 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.366+02:00 ERROR 20960 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@39164e7c: Master is null.
2025-05-28T13:14:55.425+02:00  INFO 20960 --- [Thread-10] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.425+02:00 ERROR 20960 --- [Thread-10] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@59b777ff: Master is null.
2025-05-28T13:14:55.639+02:00  INFO 20960 --- [Thread-11] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.639+02:00 ERROR 20960 --- [Thread-11] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4dbea20: Master is null.
2025-05-28T13:14:55.780+02:00  INFO 20960 --- [Thread-12] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.780+02:00 ERROR 20960 --- [Thread-12] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@289a9a46: Master is null.
2025-05-28T13:14:55.926+02:00  INFO 20960 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.926+02:00 ERROR 20960 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@38863572: Master is null.
2025-05-28T13:14:55.980+02:00  INFO 20960 --- [Thread-13] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:55 CEST 2025
2025-05-28T13:14:55.980+02:00 ERROR 20960 --- [Thread-13] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@d4cfc74: Master is null.
2025-05-28T13:14:56.046+02:00  INFO 20960 --- [Thread-14] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.047+02:00 ERROR 20960 --- [Thread-14] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@5c676d4e: Master is null.
2025-05-28T13:14:56.181+02:00  INFO 20960 --- [Thread-15] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.181+02:00 ERROR 20960 --- [Thread-15] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@2bfb66a0: Master is null.
2025-05-28T13:14:56.353+02:00  INFO 20960 --- [Thread-16] c.h.e.services.FloorReaderService        : Processing request on floor 2 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.353+02:00 ERROR 20960 --- [Thread-16] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1500b37a: Master is null.
2025-05-28T13:14:56.747+02:00  INFO 20960 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.747+02:00 ERROR 20960 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@501d55b6: Master is null.
2025-05-28T13:14:56.806+02:00  INFO 20960 --- [Thread-17] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.806+02:00 ERROR 20960 --- [Thread-17] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4f403ba9: Master is null.
2025-05-28T13:14:56.921+02:00  INFO 20960 --- [Thread-18] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.922+02:00 ERROR 20960 --- [Thread-18] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@4e9213f0: Master is null.
2025-05-28T13:14:56.995+02:00  INFO 20960 --- [Thread-19] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:56 CEST 2025
2025-05-28T13:14:56.995+02:00 ERROR 20960 --- [Thread-19] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@1574d1e5: Master is null.
2025-05-28T13:14:57.154+02:00  INFO 20960 --- [Thread-20] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:57 CEST 2025
2025-05-28T13:14:57.154+02:00 ERROR 20960 --- [Thread-20] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@789e71c0: Master is null.
2025-05-28T13:14:57.528+02:00  INFO 20960 --- [Thread-21] c.h.e.services.FloorReaderService        : Processing request on floor 3 at Wed May 28 13:14:57 CEST 2025
2025-05-28T13:14:57.529+02:00 ERROR 20960 --- [Thread-21] c.h.evmodbus.services.ModbusSpotReader   : Error reading spot com.hakcu.evmodbus.entities.Spot@70539fba: Master is null.
2025-05-28T13:14:59.762+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:14:59.762128500
2025-05-28T13:14:59.793+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:14:59.762128500 using time unit HOUR
2025-05-28T13:14:59.793+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-27T23:00 to 2025-05-28T14:14:59.762128500
2025-05-28T13:14:59.841+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-28T13:14:59.842+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:14:59.842+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:14:59.842+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-27T23:00:00.271275 = 1557.146, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:14:59.842+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.005004883, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.005004883, 05:00 - 06:00=0.0059814453, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.0059814453, 09:00 - 10:00=0.0040283203, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-28T13:14:59.923+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:14:59.923148700
2025-05-28T13:14:59.952+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:14:59.923148700 using time unit DAY
2025-05-28T13:14:59.952+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-29T23:59:59
2025-05-28T13:15:00.000+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:00.000+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:00.000+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:00.000+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.000+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953}
2025-05-28T13:15:00.079+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-28T13:15:00.126+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-28T13:15:00.126+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 4252 readings in extended date range
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=164.56702, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-28T13:15:00.174+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=164.56702, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-28T13:15:00.269+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.269733200
2025-05-28T13:15:00.301+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.269733200 using time unit HOUR
2025-05-28T13:15:00.301+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-27T23:00 to 2025-05-28T14:15:00.269733200
2025-05-28T13:15:00.333+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-28T13:15:00.348+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.348+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.348+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-27T23:00:00.271275 = 1557.146, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.348+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.005004883, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.005004883, 05:00 - 06:00=0.0059814453, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.0059814453, 09:00 - 10:00=0.0040283203, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-28T13:15:00.380+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.380627500
2025-05-28T13:15:00.412+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.380627500 using time unit HOUR
2025-05-28T13:15:00.412+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-27T23:00 to 2025-05-28T14:15:00.380627500
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-27T23:00:00.271275 = 1557.146, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.005004883, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.005004883, 05:00 - 06:00=0.0059814453, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.0059814453, 09:00 - 10:00=0.0040283203, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-28T13:15:00.459+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999
2025-05-28T13:15:00.507+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999 using time unit DAY
2025-05-28T13:15:00.507+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-06-01T23:59:59
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953, 29/05=0.0, 30/05=0.0, 31/05=0.0}
2025-05-28T13:15:00.539+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.539844300
2025-05-28T13:15:00.588+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-28T00:00 to 2025-05-28T13:15:00.539844300 using time unit HOUR
2025-05-28T13:15:00.588+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-27T23:00 to 2025-05-28T14:15:00.539844300
2025-05-28T13:15:00.619+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Found 15 readings in extended date range
2025-05-28T13:15:00.619+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Generated 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.619+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 14 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00]
2025-05-28T13:15:00.619+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-27T23:00:00.271275 = 1557.146, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:00.619+02:00  INFO 20960 --- [http-nio-8080-exec-2] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.005004883, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.005004883, 05:00 - 06:00=0.0059814453, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.005004883, 08:00 - 09:00=0.0059814453, 09:00 - 10:00=0.0040283203, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0}
2025-05-28T13:15:30.378+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:30.426+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:30.426+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:30.458+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:30.458+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:30.458+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:30.458+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:30.458+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:30.540+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:15:30.540106800
2025-05-28T13:15:30.571+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:15:30.540106800 using time unit DAY
2025-05-28T13:15:30.571+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-29T23:59:59
2025-05-28T13:15:30.617+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:30.617+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:30.617+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:30.617+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:30.617+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953}
2025-05-28T13:15:30.697+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999
2025-05-28T13:15:30.730+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-01-01T00:00 to 2025-12-31T23:59:59.999999999 using time unit MONTH
2025-05-28T13:15:30.730+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2024-12-01T00:00 to 2026-01-31T23:59:59
2025-05-28T13:15:30.777+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 4252 readings in extended date range
2025-05-28T13:15:30.777+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:30.777+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:30.777+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-12-01T00:00:00.048312 = 875.367, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:30.793+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=164.56702, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-28T13:15:30.793+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-28T13:15:30.793+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=77.028015, Feb=101.33801, Mar=101.36096, Abr=160.03992, May=164.56702, Jun=0.0, Jul=0.0, Ago=0.0, Sep=0.0, Oct=0.0, Nov=0.0, Dic=0.0}
2025-05-28T13:15:30.872+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:30.904+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:30.904+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:30.951+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:30.951+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:30.951+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:30.951+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:30.951+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:30.983+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:31.031+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:31.031+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:31.063+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999
2025-05-28T13:15:31.095+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999 using time unit DAY
2025-05-28T13:15:31.095+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-06-01T23:59:59
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953, 29/05=0.0, 30/05=0.0, 31/05=0.0}
2025-05-28T13:15:31.143+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:31.174+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:31.174+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:31.222+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:31.222+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:31.222+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:31.222+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:31.222+02:00  INFO 20960 --- [http-nio-8080-exec-5] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:47.595+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:47.634+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:47.634+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:47.674+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:47.674+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:47.674+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:47.674+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:47.674+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:47.740+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:15:47.740683200
2025-05-28T13:15:47.786+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-28T13:15:47.740683200 using time unit DAY
2025-05-28T13:15:47.786+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-05-29T23:59:59
2025-05-28T13:15:47.829+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:47.829+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:47.829+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 28 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05]
2025-05-28T13:15:47.829+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:47.831+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953}
2025-05-28T13:15:47.898+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting monthly consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999
2025-05-28T13:15:47.946+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2024-01-01T00:00 to 2024-12-31T23:59:59.999999999 using time unit MONTH
2025-05-28T13:15:47.946+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2023-12-01T00:00 to 2025-01-31T23:59:59
2025-05-28T13:15:47.995+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 5439 readings in extended date range
2025-05-28T13:15:47.995+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:47.995+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 12 periods: [Ene, Feb, Mar, Abr, May, Jun, Jul, Ago, Sep, Oct, Nov, Dic]
2025-05-28T13:15:47.995+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2024-06-13T18:00:00.262571 = 385.49, Last reading: 2025-01-31T23:00:00.047367 = 1027.671
2025-05-28T13:15:48.009+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-28T13:15:48.009+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Ensuring all months are included in yearly view
2025-05-28T13:15:48.009+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map with all months: {Ene=0.0, Feb=0.0, Mar=0.0, Abr=0.0, May=0.0, Jun=49.399017, Jul=118.014984, Ago=69.617004, Sep=75.75897, Oct=98.82501, Nov=78.252014, Dic=75.276}
2025-05-28T13:15:48.088+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:48.120+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:48.120+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:48.167+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:48.167+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.167+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.167+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:48.167+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:48.207+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:48.248+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:48.248+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:15:48.282+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting daily consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999
2025-05-28T13:15:48.311+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-01T00:00 to 2025-05-31T23:59:59.999999999 using time unit DAY
2025-05-28T13:15:48.311+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-04-30T00:00 to 2025-06-01T23:59:59
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 686 readings in extended date range
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 31 periods: [01/05, 02/05, 03/05, 04/05, 05/05, 06/05, 07/05, 08/05, 09/05, 10/05, 11/05, 12/05, 13/05, 14/05, 15/05, 16/05, 17/05, 18/05, 19/05, 20/05, 21/05, 22/05, 23/05, 24/05, 25/05, 26/05, 27/05, 28/05, 29/05, 30/05, 31/05]
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-04-30T00:00:00.050260 = 1392.637, Last reading: 2025-05-28T13:00:00.279508 = 1557.204
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {01/05=10.116089, 02/05=0.12194824, 03/05=10.139038, 04/05=0.0859375, 05/05=0.0, 06/05=8.721069, 07/05=0.123046875, 08/05=0.052978516, 09/05=10.302002, 10/05=11.210083, 11/05=0.12207031, 12/05=8.921997, 13/05=9.809937, 14/05=10.012085, 15/05=9.89502, 16/05=10.061035, 17/05=7.4050293, 18/05=0.5050049, 19/05=0.048950195, 20/05=9.799072, 21/05=0.049926758, 22/05=10.05603, 23/05=0.123046875, 24/05=4.3519287, 25/05=3.4520264, 26/05=7.526001, 27/05=10.470947, 28/05=0.052001953, 29/05=0.0, 30/05=0.0, 31/05=0.0}
2025-05-28T13:15:48.358+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Getting hourly consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999
2025-05-28T13:15:48.390+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Calculating consumption for spot 13 from 2025-05-27T00:00 to 2025-05-27T23:59:59.999999999 using time unit HOUR
2025-05-28T13:15:48.390+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Extended date range: 2025-05-26T23:00 to 2025-05-28T00:59:59.999999999
2025-05-28T13:15:48.439+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Found 26 readings in extended date range
2025-05-28T13:15:48.439+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Generated 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.439+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Initialized consumption map with 24 periods: [00:00 - 01:00, 01:00 - 02:00, 02:00 - 03:00, 03:00 - 04:00, 04:00 - 05:00, 05:00 - 06:00, 06:00 - 07:00, 07:00 - 08:00, 08:00 - 09:00, 09:00 - 10:00, 10:00 - 11:00, 11:00 - 12:00, 12:00 - 13:00, 13:00 - 14:00, 14:00 - 15:00, 15:00 - 16:00, 16:00 - 17:00, 17:00 - 18:00, 18:00 - 19:00, 19:00 - 20:00, 20:00 - 21:00, 21:00 - 22:00, 22:00 - 23:00, 23:00 - 00:00]
2025-05-28T13:15:48.439+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : First reading: 2025-05-26T23:00:00.200014 = 1544.503, Last reading: 2025-05-28T00:00:00.223723 = 1557.152
2025-05-28T13:15:48.439+02:00  INFO 20960 --- [http-nio-8080-exec-7] c.h.e.s.c.ConsumptionCalculationService  : Final consumption map: {00:00 - 01:00=0.22692871, 01:00 - 02:00=0.0059814453, 02:00 - 03:00=0.005004883, 03:00 - 04:00=0.005004883, 04:00 - 05:00=0.0059814453, 05:00 - 06:00=0.005004883, 06:00 - 07:00=0.005004883, 07:00 - 08:00=0.0059814453, 08:00 - 09:00=0.005004883, 09:00 - 10:00=0.0020751953, 10:00 - 11:00=0.0, 11:00 - 12:00=0.0, 12:00 - 13:00=0.0, 13:00 - 14:00=0.0, 14:00 - 15:00=0.0, 15:00 - 16:00=0.0, 16:00 - 17:00=0.0, 17:00 - 18:00=0.0, 18:00 - 19:00=2.290039, 19:00 - 20:00=2.3829346, 20:00 - 21:00=2.3719482, 21:00 - 22:00=2.368042, 22:00 - 23:00=0.78601074, 23:00 - 00:00=0.0059814453}
2025-05-28T13:22:07.490+02:00  INFO 20960 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-28T13:22:07.506+02:00  INFO 20960 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-28T13:22:07.510+02:00  INFO 20960 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:22:07.510+02:00  INFO 20960 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-28T13:22:07.672+02:00  INFO 20960 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
