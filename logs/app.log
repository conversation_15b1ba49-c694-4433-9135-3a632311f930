2025-05-28T11:53:17.527+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 6272 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-28T11:53:17.536+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-28T11:53:17.587+02:00  INFO 6272 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-28T11:53:17.587+02:00  INFO 6272 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-28T11:53:18.392+02:00  INFO 6272 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28T11:53:18.500+02:00  INFO 6272 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 94 ms. Found 10 JPA repository interfaces.
2025-05-28T11:53:19.127+02:00  INFO 6272 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-28T11:53:19.142+02:00  INFO 6272 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-28T11:53:19.142+02:00  INFO 6272 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-28T11:53:19.187+02:00  INFO 6272 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-28T11:53:19.187+02:00  INFO 6272 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1600 ms
2025-05-28T11:53:19.414+02:00  INFO 6272 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28T11:53:19.472+02:00  INFO 6272 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-28T11:53:19.506+02:00  INFO 6272 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-28T11:53:19.763+02:00  INFO 6272 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-28T11:53:19.796+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-28T11:53:20.165+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@74a3764d
2025-05-28T11:53:20.167+02:00  INFO 6272 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-28T11:53:20.228+02:00  INFO 6272 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-28T11:53:21.237+02:00  INFO 6272 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-28T11:53:22.896+02:00  INFO 6272 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T11:53:22.937+02:00  WARN 6272 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-28T11:53:23.177+02:00  INFO 6272 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-28T11:53:24.977+02:00  WARN 6272 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-28T11:53:25.029+02:00  INFO 6272 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-28T11:53:25.285+02:00  INFO 6272 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-28T11:53:25.797+02:00  INFO 6272 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-28T11:53:25.827+02:00  INFO 6272 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-28T11:53:25.842+02:00  INFO 6272 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 8.691 seconds (process running for 9.756)
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-28T12:16:37.831+02:00  INFO 6272 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-05-28T12:16:41.010+02:00  WARN 6272 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:41.010+02:00  WARN 6272 --- [http-nio-8080-exec-6] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:44.603+02:00  WARN 6272 --- [http-nio-8080-exec-9] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:16:44.603+02:00  WARN 6272 --- [http-nio-8080-exec-9] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T12:20:45.683+02:00  INFO 6272 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-28T12:20:45.683+02:00  INFO 6272 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-28T12:20:45.699+02:00  INFO 6272 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-05-28T13:13:55.835+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Starting EvModbusApplication using Java 21.0.7 with PID 15052 (C:\Project\build\classes\java\main started by david in C:\Project)
2025-05-28T13:13:55.837+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : No active profile set, falling back to 1 default profile: "default"
2025-05-28T13:13:55.875+02:00  INFO 15052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-28T13:13:55.875+02:00  INFO 15052 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-28T13:13:56.518+02:00  INFO 15052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-28T13:13:56.595+02:00  INFO 15052 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 70 ms. Found 10 JPA repository interfaces.
2025-05-28T13:13:57.102+02:00  INFO 15052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-05-28T13:13:57.113+02:00  INFO 15052 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-05-28T13:13:57.113+02:00  INFO 15052 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-05-28T13:13:57.154+02:00  INFO 15052 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-05-28T13:13:57.155+02:00  INFO 15052 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1280 ms
2025-05-28T13:13:57.331+02:00  INFO 15052 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-05-28T13:13:57.387+02:00  INFO 15052 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.6.15.Final
2025-05-28T13:13:57.414+02:00  INFO 15052 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-05-28T13:13:57.648+02:00  INFO 15052 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-05-28T13:13:57.674+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-05-28T13:13:57.954+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@726ca1a3
2025-05-28T13:13:57.955+02:00  INFO 15052 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-05-28T13:13:58.013+02:00  INFO 15052 --- [restartedMain] org.hibernate.orm.connections.pooling    : HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 16.9
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-05-28T13:13:58.869+02:00  INFO 15052 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-05-28T13:14:00.515+02:00  INFO 15052 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:14:00.551+02:00  WARN 15052 --- [restartedMain] c.h.e.components.JSerialCommModbusRTU    : No serial devices found. Serial initialization failed.
2025-05-28T13:14:00.755+02:00  INFO 15052 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-05-28T13:14:02.399+02:00  WARN 15052 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-05-28T13:14:02.439+02:00  INFO 15052 --- [restartedMain] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page template: index
2025-05-28T13:14:02.657+02:00  INFO 15052 --- [restartedMain] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name userDetailsServiceImpl
2025-05-28T13:14:03.093+02:00  INFO 15052 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-05-28T13:14:03.132+02:00  INFO 15052 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/'
2025-05-28T13:14:03.139+02:00  INFO 15052 --- [restartedMain] com.hakcu.evmodbus.EvModbusApplication   : Started EvModbusApplication in 7.673 seconds (process running for 8.546)
2025-05-28T13:14:11.599+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-28T13:14:11.599+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-05-28T13:14:11.600+02:00  INFO 15052 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-05-28T13:14:15.139+02:00  WARN 15052 --- [http-nio-8080-exec-10] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T13:14:15.139+02:00  WARN 15052 --- [http-nio-8080-exec-10] c.h.evmodbus.services.ReportingService   : No serial ports found
2025-05-28T13:14:21.453+02:00  INFO 15052 --- [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-05-28T13:14:21.453+02:00  INFO 15052 --- [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-05-28T13:14:21.468+02:00  INFO 15052 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
